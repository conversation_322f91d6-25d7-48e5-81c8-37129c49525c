{% extends 'base.html' %}
{% load static %}

{% block title %}Strona główna - Omasta Travel{% endblock %}

{% block content %}
    <section class="hero-banner" style="background-image: url('{% static 'images/omastatravel.jpg' %}');">
        <div class="hero-content text-center text-white">
            <h1 class="display-4">Witaj w Omasta Travel!</h1>
            <p class="leady">Odkryj z nami najpiękniejsze zakątki świata</p><br>
            <a class="btn btn-light btn-lg mt-3" href="{% url 'trip_list' %}" role="button">Przeglądaj wycieczki</a>
        </div>
    </section>

    <!-- Personalized recommendations -->
    <section class="recommended-trips py-5">
        <div class="container">
            <h2 class="text-center mb-4">Polecane dla Ciebie</h2>
            <div class="row row-cols-1 row-cols-md-3 g-4">
                {% for rec in recommendations %}
                    <div class="col">
                        <div class="card h-100 shadow-sm rounded">
                            {% if rec.trip.destination.image %}
                                <img src="{{ rec.trip.destination.image.url }}" class="card-img-top trip-image" alt="{{ rec.trip.title }}">
                            {% endif %}
                            <div class="card-body">
                                <h5 class="card-title">{{ rec.trip.title }}</h5>
                                <p class="card-text">{{ rec.reason }}</p>
                                <p class="card-text">
                                    <small class="text-muted">{{ rec.trip.start_date }} - {{ rec.trip.end_date }}</small>
                                </p>
                                <p class="card-text">
                                    <strong>Cena: {{ rec.trip.price }} PLN</strong>
                                </p>
                            </div>
                            <div class="card-footer">
                                <a href="{% url 'trip_detail' rec.trip.id %}" class="btn btn-primary w-100">Zobacz szczegóły</a>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Uzupełnij swoje preferencje, aby otrzymać spersonalizowane rekomendacje!
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </section>
    
    <section class="recommended-trips py-5">
        <div class="container">
            <h2 class="text-center mb-4">Wycieczki Last Minute</h2>
            <div class="row row-cols-1 row-cols-md-3 g-4">
                {% for trip in trips %}
                    <div class="col">
                        <div class="card h-100 shadow-sm rounded">
                            {% if trip.destination.image %}
                                <img src="{{ trip.destination.image.url }}" class="card-img-top trip-image" alt="{{ trip.title }}">
                            {% endif %}
                            <div class="card-body">
                                <h5 class="card-title">{{ trip.title }}</h5>
                                <p class="card-text">{{ trip.description|truncatewords:30 }}</p>
                                <p class="card-text">
                                    <small class="text-muted">{{ trip.start_date }} - {{ trip.end_date }}</small>
                                </p>
                                <p class="card-text">
                                    <strong>Cena: {{ trip.price }} PLN</strong>
                                </p>
                            </div>
                            <div class="card-footer">
                                <a href="{% url 'trip_detail' trip.pk %}" class="btn btn-primary w-100">Zobacz szczegóły</a>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="col-12">
                        <p class="text-center">Brak dostępnych wycieczek.</p>
                    </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Browsing history -->
    <section class="recommended-trips py-5">
        <div class="container">
            <h2 class="text-center mb-4">Ostatnio przeglądane</h2>
            <div class="row row-cols-1 row-cols-md-3 g-4">
                {% for history in browsing_history %}
                    <div class="col">
                        <div class="card h-100 shadow-sm rounded">
                            {% if history.trip.destination.image %}
                                <img src="{{ history.trip.destination.image.url }}" class="card-img-top trip-image" alt="{{ history.trip.title }}">
                            {% endif %}
                            <div class="card-body">
                                <h5 class="card-title">{{ history.trip.title }}</h5>
                                <p class="card-text">{{ history.trip.description|truncatewords:30 }}</p>
                                <p class="card-text">
                                    <small class="text-muted">{{ history.trip.start_date }} - {{ history.trip.end_date }}</small>
                                </p>
                                <p class="card-text">
                                    <strong>Cena: {{ history.trip.price }} PLN</strong>
                                </p>
                                <p class="card-text">
                                    <small class="text-muted">Oglądane {{ history.view_count }} raz(y)</small>
                                </p>
                            </div>
                            <div class="card-footer">
                                <a href="{% url 'trip_detail' history.trip.id %}" class="btn btn-primary w-100">Zobacz szczegóły</a>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="col-12">
                        <p class="text-center">Brak historii przeglądania.</p>
                    </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Upcoming reservations -->
    <section class="recommended-trips py-5">
        <div class="container">
            <h2 class="text-center mb-4">Nadchodzące podróże</h2>
            <div class="row row-cols-1 row-cols-md-3 g-4">
                {% for reservation in upcoming_reservations %}
                    <div class="col">
                        <div class="card h-100 shadow-sm rounded">
                            {% if reservation.trip.destination.image %}
                                <img src="{{ reservation.trip.destination.image.url }}" class="card-img-top trip-image" alt="{{ reservation.trip.title }}">
                            {% endif %}
                            <div class="card-body">
                                <h5 class="card-title">{{ reservation.trip.title }}</h5>
                                <p class="card-text">{{ reservation.trip.description|truncatewords:30 }}</p>
                                <p class="card-text">
                                    <small class="text-muted">{{ reservation.trip.start_date }} - {{ reservation.trip.end_date }}</small>
                                </p>
                                <p class="card-text">
                                    <span class="badge bg-success">{{ reservation.get_status_display }}</span>
                                </p>
                            </div>
                            <div class="card-footer">
                                <a href="{% url 'reservation_detail' reservation.id %}" class="btn btn-primary w-100">Szczegóły</a>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="col-12">
                        <p class="text-center">Brak nadchodzących podróży.</p>
                    </div>
                {% endfor %}
            </div>
        </div>
    </section>
{% endblock %}  