{% extends 'base.html' %}
{% block title %}Szczegóły zgłoszenia{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col d-flex justify-content-between align-items-center">
            <h2 class="mb-0">Zgłoszenie #{{ ticket.id }}: {{ ticket.subject }}</h2>
            <a href="{% url 'my_tickets' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Powrót
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-body" style="height: 600px; overflow-y: auto;">
                    {% for msg in ticket_messages %}
                        <div class="d-flex mb-4 {% if msg.user == ticket.user %}justify-content-end{% endif %}">
                            <div class="card {% if msg.user == ticket.user %}bg-primary text-white{% else %}bg-light{% endif %}" style="max-width: 75%;">
                                <div class="card-header bg-transparent {% if msg.user == ticket.user %}border-0 text-white{% endif %}">
                                    <div class="d-flex justify-content-between align-items-center gap-2">
                                        <strong>{% if msg.user == ticket.user %}Ty{% else %}Admin{% endif %}</strong>
                                        <small>{{ msg.created_at|date:"d.m.Y H:i" }}</small>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <p class="text-light mb-0">{{ msg.text }}</p>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                {% if ticket.status == 'new' or ticket.status == 'in_progress' %}
                    <div class="card-footer bg-light">
                        <form method="post">
                            {% csrf_token %}
                            <div class="mb-3">
                                {{ form.text.label_tag }}
                                {{ form.text }}
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-send"></i> Wyślij
                            </button>
                        </form>
                    </div>
                {% else %}
                    <div class="card-footer text-center text-muted">
                        Zgłoszenie zostało zamknięte.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}