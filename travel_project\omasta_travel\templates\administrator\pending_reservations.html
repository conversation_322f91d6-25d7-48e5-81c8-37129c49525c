{% extends 'base.html' %}

{% block title %}Oczekuj<PERSON><PERSON> Rezerwacje - Panel Administratora{% endblock %}

{% block content %}
{% if not captcha_valid %}
    <h2 class="mb-4">Weryfikacja CAPTCHA</h2>
    <p>Przed uzyskaniem dostępu do panelu administratora, prosimy o rozwiązanie CAPTCHA.</p>
    {% if messages %}
        <ul class="messages">
            {% for message in messages %}
                <li{% if message.tags %} class="{{ message.tags }}"{% endif %}>{{ message }}</li>
            {% endfor %}
        </ul>
    {% endif %}
    <form method="post" action="{% url 'administrator_pending_reservations' %}">
        {% csrf_token %}
        {{ captcha_form.as_p }}
        <button type="submit" class="btn btn-primary">Zweryfikuj</button>
    </form>
{% else %}
    <h2 class="mb-4"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Rezerwacje</h2>

    <div class="table-responsive">
    <table class="table table-striped table-hover table-rounded">
        <thead class="table-custom bg-warning">
            <tr>
                    <th>ID</th>
                    <th>Użytkownik</th>
                    <th>Wycieczka</th>
                    <th>Liczba osób</th>
                    <th>Całkowita cena</th>
                    <th>Data rezerwacji</th>
                    <th>Akcje</th>
                </tr>
            </thead>
            <tbody>
                {% for reservation in reservations %}
                <tr>
                    <td>{{ reservation.id }}</td>
                    <td>{{ reservation.user.username }}</td>
                    <td>{{ reservation.trip.title }}</td>
                    <td>{{ reservation.number_of_people }}</td>
                    <td>{{ reservation.total_price }} PLN</td>
                    <td>{{ reservation.created_at|date:"d.m.Y H:i" }}</td>
                    <td>
                        <div class="d-flex align-items-center gap-1">
                            <!-- Formularz zmiany statusu -->
                            <form method="post" action="{% url 'administrator_reservation_status' reservation.id %}" class="d-flex gap-1">
                                {% csrf_token %}
                                <select name="status" class="form-select form-select-sm w-auto">
                                    {% for value, label in reservation.STATUS_CHOICES %}
                                    <option value="{{ value }}" {% if reservation.status == value %}selected{% endif %}>
                                        {{ label }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <button type="submit" class="btn btn-sm btn-primary">
                                    ✓
                                </button>
                            </form>
                    
                            <!-- Grupa przycisków -->
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{% url 'reservation_detail' reservation.id %}" class="btn btn-sm btn-info">
                                    <i class="bi bi-eye"></i> Szczegóły
                                </a>
                                <form method="post" action="{% url 'delete_reservation' reservation.id %}"
                                    onsubmit="return confirm('Czy na pewno chcesz usunąć tę rezerwację?');">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="bi bi-trash"></i> Usuń
                                    </button>
                                </form>
                            </div>
                        </div>
                    </td>                                             
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="text-center">Brak oczekujących rezerwacji.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% endif %}
{% endblock %}
