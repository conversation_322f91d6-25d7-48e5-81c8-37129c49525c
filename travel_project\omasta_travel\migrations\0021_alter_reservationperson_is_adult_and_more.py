# Generated by Django 5.1.4 on 2025-02-26 18:14

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('omasta_travel', '0020_trip_image_alter_destination_image'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='reservationperson',
            name='is_adult',
            field=models.BooleanField(default=True, help_text='<PERSON><PERSON><PERSON><PERSON>, jeśli osoba jest dorosła.'),
        ),
        migrations.AlterField(
            model_name='reservationperson',
            name='pesel',
            field=models.CharField(help_text='PESEL musi mieć 11 cyfr.', max_length=11, validators=[django.core.validators.MinLengthValidator(11, message='PESEL musi mieć 11 cyfr.'), django.core.validators.MaxLengthValidator(11, message='PESEL musi mieć 11 cyfr.'), django.core.validators.RegexValidator('^\\d+$', message='PESEL może zawierać tylko cyfry.')]),
        ),
    ]
