from django.urls import path
from . import views
from django.contrib.auth import views as auth_views


urlpatterns = [
    # Public routes
    path('', views.home, name='home'),
    path('countries/', views.country_list, name='country_list'),
    path('countries/<int:country_id>/destinations/', views.country_destinations, name='country_destinations'),
    path('trips/', views.trip_list, name='trip_list'),  # Wszystkie wycieczki
    path('destinations/<int:destination_id>/trips/', views.trip_list, name='destination_trips'),
    path('trip/<int:pk>/', views.trip_detail, name='trip_detail'),
    path('ticket/create/', views.create_ticket, name='create_ticket'),
    path('my-tickets/', views.my_tickets, name='my_tickets'),
    path('my-tickets/<int:ticket_id>/', views.ticket_detail, name='ticket_detail'),
    path('my-tickets/delete/<int:ticket_id>/', views.delete_ticket, name='delete_ticket'),
    path('chat/', views.chatbot_view, name='chat'),
    path('ask-question/', views.ask_question, name='ask_question'),
    path('clear-chat/', views.clear_chat_history, name='clear_chat_history'),
    path('preferences/', views.user_preferences, name='user_preferences'),

    # Authentication
    path('register/', views.register, name='register'),
    path('login/', auth_views.LoginView.as_view(template_name='registration/login.html'), name='login'),
    path('logout/', auth_views.LogoutView.as_view(next_page='home'), name='logout'),

    # User routes
    path('reservations/', views.user_reservations, name='user_reservations'),
    path('reservation/details/', views.making_reservation, name='making_reservation'),
    path('reservation/<int:reservation_id>/delete/', views.delete_reservation, name='delete_reservation'),
    path('reservation/<int:reservation_id>/', views.reservation_detail, name='reservation_detail'),
    path(
        'reservation/<int:reservation_id>/download/',
        views.download_reservation_pdf,
        name='download_reservation_pdf'
    ),
    path('trip/<int:trip_id>/review/', views.add_review, name='add_review'),
    path('trip/<int:trip_id>/review/<int:review_id>/delete/', views.delete_review, name='delete_review'),

    # Admin routes
    path('administrator/dashboard/', views.administrator_dashboard, name='administrator_dashboard'),
    path('administrator/reservations/pending/', views.administrator_pending_reservations, name='administrator_pending_reservations'),
    path('administrator/reservations/all/', views.administrator_all_reservations, name='administrator_all_reservations'),
    path('administrator/reservation/<int:reservation_id>/status/', views.administrator_reservation_status, name='administrator_reservation_status'),
    path('administrator/tickets/', views.list_tickets_admin, name='list_tickets_admin'),
    path('administrator/tickets/delete/<int:ticket_id>/', views.delete_ticket, name='delete_ticket'),
    path('administrator/business-analysis/', views.business_analysis, name='business_analysis'),


]
