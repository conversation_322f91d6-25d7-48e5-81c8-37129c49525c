# Generated by Django 5.1.4 on 2025-01-22 21:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('omasta_travel', '0007_alter_country_continent'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='trip',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='trip',
            name='image',
        ),
        migrations.AddField(
            model_name='trip',
            name='typ_wakacji',
            field=models.CharField(choices=[('vacation', 'Wczasy'), ('city_break', 'City Break'), ('tour', 'Wycieczki objazdowe')], default='vacation', max_length=20, verbose_name='Typ wakacji'),
        ),
        migrations.AddField(
            model_name='trip',
            name='wyzywienie',
            field=models.CharField(choices=[('none', 'Brak'), ('all_inclusive', 'All Inclusive'), ('breakfast', 'Śniadanie'), ('half_board', 'Śniadanie + Obiadokolacja'), ('full_board', 'Pełne wyżywienie')], default='none', max_length=20, verbose_name='Wyżywienie'),
        ),
        migrations.AlterField(
            model_name='trip',
            name='price',
            field=models.DecimalField(decimal_places=2, max_digits=10),
        ),
    ]
