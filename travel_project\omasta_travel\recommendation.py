import google.generativeai as genai
from django.conf import settings
import json
import re
from .models import Trip, UserPreference, UserBrowsingHistory

# Configure the API
genai.configure(api_key=settings.GEMINI_API_KEY)

def get_gemini_model():
    return genai.GenerativeModel('gemini-2.0-flash')

def track_user_view(user, trip):
    """Track when a user views a trip"""
    if not user.is_authenticated:
        return
    
    # Update or create browsing history entry
    history, created = UserBrowsingHistory.objects.get_or_create(
        user=user,
        trip=trip,
        defaults={'view_count': 1}
    )
    
    if not created:
        history.view_count += 1
        history.save()

def get_user_profile_data(user):
    """Get all relevant user data for recommendations"""
    if not user.is_authenticated:
        return None
    
    try:
        # Get user preferences
        preferences = UserPreference.objects.get(user=user)
        
        # Get browsing history
        recent_history = UserBrowsingHistory.objects.filter(
            user=user
        ).order_by('-last_viewed')[:10]
        
        # Get reservation history
        reservations = user.reservation_set.all().select_related('trip')
        
        profile_data = {
            "preferences": {
                "interests": preferences.interests,
                "travel_style": preferences.travel_style,
                "preferred_continents": preferences.preferred_continents,
                "budget_range": [float(preferences.budget_min) if preferences.budget_min else None, 
                                float(preferences.budget_max) if preferences.budget_max else None],
                "climate_preference": preferences.climate_preference,
                "preferred_meal_plans": preferences.preferred_meal_plans,
                "preferred_transport": preferences.preferred_transport,
                "travel_duration": [preferences.travel_duration_min, preferences.travel_duration_max],
            },
            "browsing_history": [
                {
                    "trip_id": h.trip.id,
                    "trip_title": h.trip.title,
                    "destination": h.trip.destination.name,
                    "country": h.trip.destination.country.name,
                    "continent": h.trip.destination.country.continent,
                    "price": float(h.trip.price),
                    "view_count": h.view_count,
                    "last_viewed": h.last_viewed.isoformat(),
                } for h in recent_history
            ],
            "reservations": [
                {
                    "trip_id": r.trip.id,
                    "trip_title": r.trip.title,
                    "destination": r.trip.destination.name,
                    "country": r.trip.destination.country.name,
                    "continent": r.trip.destination.country.continent,
                    "price": float(r.trip.price),
                    "date_reserved": r.created_at.isoformat(),
                    "status": r.status,
                } for r in reservations
            ]
        }
        
        return profile_data
        
    except UserPreference.DoesNotExist:
        return None

def get_active_trips_data(limit=50):
    """Get data about active trips for recommendations"""
    active_trips = Trip.objects.filter(
        is_active=True, 
        available_places__gt=0
    ).select_related('destination__country')[:limit]
    
    trips_data = [
        {
            "id": trip.id,
            "title": trip.title,
            "destination": trip.destination.name,
            "country": trip.destination.country.name,
            "continent": trip.destination.country.continent,
            "price": float(trip.price),
            "start_date": trip.start_date.isoformat(),
            "end_date": trip.end_date.isoformat(),
            "duration": (trip.end_date - trip.start_date).days,
            "transport": trip.transport,
            "trip_type": trip.trip_type,
            "meal_plan": trip.meal_plan,
            "available_places": trip.available_places,
        } for trip in active_trips
    ]
    
    return trips_data

def get_trip_recommendations(user, count=5):
    """Synchronous version of trip recommendations"""
    print("Starting trip recommendations")
    model = get_gemini_model()
    
    user_profile = get_user_profile_data(user)
    print(f"User profile: {user_profile is not None}")
    
    available_trips = get_active_trips_data()
    print(f"Available trips: {len(available_trips)}")
    
    if not user_profile or not available_trips:
        # Return random trips if we don't have enough data
        return Trip.objects.filter(is_active=True)[:count]
    
    prompt = f"""
    As a travel recommendation system, your task is to recommend the most suitable trips for a user based on their profile and browsing history.
    
    USER PROFILE:
    {json.dumps(user_profile, indent=2)}
    
    AVAILABLE TRIPS:
    {json.dumps(available_trips, indent=2)}
    
    Based on this information, please recommend the top {count} trips from the available trips that best match the user's preferences, browsing history, and past reservations.
    
    Consider the following factors in your recommendation:
    1. Match with user's interests and travel style
    2. Match with preferred continents and climate
    3. Price within the user's budget range
    4. Appropriate trip duration
    5. Preferred meal plans and transport
    6. Recent browsing history (with higher weight for frequently viewed trips)
    7. Past reservation patterns
    
    Return your response as a JSON array containing only the trip IDs of the recommended trips, sorted by relevance.
    Example: [34, 12, 56, 78, 90]
    """
    
    try:
        # Use the synchronous API instead of async
        response = model.generate_content(prompt)
        response_text = response.text
        
        # Extract JSON array from response
        json_match = re.search(r'\[.*?\]', response_text, re.DOTALL)
        if json_match:
            trip_ids = json.loads(json_match.group(0))
            
            # Get the recommended trips
            recommended_trips = []
            for trip_id in trip_ids:
                try:
                    trip = Trip.objects.get(id=trip_id, is_active=True)
                    recommended_trips.append(trip)
                except Trip.DoesNotExist:
                    continue
            
            # If we couldn't get enough recommendations, add some default ones
            if len(recommended_trips) < count:
                existing_ids = [trip.id for trip in recommended_trips]
                additional_trips = Trip.objects.filter(is_active=True).exclude(id__in=existing_ids)[:count-len(recommended_trips)]
                recommended_trips.extend(additional_trips)
            
            return recommended_trips[:count]
        
    except Exception as e:
        print(f"Error getting recommendations: {e}")
    
    # Fallback to default recommendations
    return Trip.objects.filter(is_active=True)[:count]

def get_recommendation_explanation(user, trip):
    """Get an explanation for why this trip was recommended"""
    model = get_gemini_model()
    
    user_profile = get_user_profile_data(user)
    if not user_profile:
        return "Ta wycieczka może Cię zainteresować."
    
    trip_data = {
        "id": trip.id,
        "title": trip.title,
        "destination": trip.destination.name,
        "country": trip.destination.country.name,
        "continent": trip.destination.country.continent,
        "price": float(trip.price),
        "start_date": trip.start_date.isoformat(),
        "end_date": trip.end_date.isoformat(),
        "duration": (trip.end_date - trip.start_date).days,
        "transport": trip.transport,
        "trip_type": trip.trip_type,
        "meal_plan": trip.meal_plan,
        "description": trip.description,
    }
    
    prompt = f"""
    As a travel recommendation system, explain in a concise, personalized way (1-2 sentences) why this specific trip would be a good match for this user based on their profile and browsing history.
    
    USER PROFILE:
    {json.dumps(user_profile, indent=2)}
    
    RECOMMENDED TRIP:
    {json.dumps(trip_data, indent=2)}
    
    Return only the brief, personalized explanation in Polish language, focused on the specific aspects of the trip that match the user's preferences.
    """
    
    try:
        response = model.generate_content(prompt)
        return response.text.strip()
    except Exception as e:
        print(f"Error getting explanation: {e}")
        return "Ta wycieczka pasuje do Twoich preferencji podróżniczych."