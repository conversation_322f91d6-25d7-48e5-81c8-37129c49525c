from django.contrib import admin
from .models import Country, Destination, Trip, Reservation, Review, ReservationPerson, Ticket, Message, UserPreference, UserBrowsingHistory, ChatMessage



@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    search_fields = ['name']


@admin.register(Destination)
class DestinationAdmin(admin.ModelAdmin):
    list_display = ['name', 'country']
    search_fields = ['name', 'country']

@admin.register(Trip)
class TripAdmin(admin.ModelAdmin):
    list_display = [
        'title',
        'destination',
        'start_date',
        'end_date',
        'price',
        'trip_type',
        'meal_plan',
        'transport',
        'is_active'
    ]
    list_filter = ['trip_type', 'meal_plan', 'transport', 'is_active']
    search_fields = ['title', 'description']
    date_hierarchy = 'start_date'

@admin.register(ReservationPerson)
class ReservationPersonAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'reservation', 'is_adult')
    search_fields = ('first_name', 'last_name', 'reservation__id')
    list_filter = ('is_adult',)
    ordering = ('reservation',)

@admin.register(Reservation)
class ReservationAdmin(admin.ModelAdmin):
    list_display = [
        'user',
        'trip',
        'number_of_people',
        'total_price',
        'status',
        'created_at',
        'reservation_code',           # NOWE POLE
        'reservation_document'        # NOWE POLE
    ]
    list_filter = ['status', 'created_at']
    search_fields = ['user__username', 'trip__title', 'reservation_code']
    date_hierarchy = 'created_at'
    readonly_fields = ['reservation_code']  # 

@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ['user', 'trip', 'rating', 'created_at']
    list_filter = ['rating', 'created_at']
    search_fields = ['user__username', 'trip__title', 'comment']

@admin.register(Ticket)
class TicketAdmin(admin.ModelAdmin):
    list_display = ('id', 'subject', 'user', 'status', 'created_at', 'updated_at')
    list_filter = ('status', 'created_at', 'updated_at')
    search_fields = ('subject', 'email', 'user__username')
    ordering = ('-created_at',)
    
@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'ticket', 'user', 'created_at')
    search_fields = ('ticket__subject', 'user__username', 'text')
    ordering = ('-created_at',)
    
@admin.register(UserPreference)
class UserPreferenceAdmin(admin.ModelAdmin):
    list_display = ('user', 'climate_preference', 'last_updated')
    search_fields = ('user__username',)
    list_filter = ('climate_preference',)

@admin.register(UserBrowsingHistory)
class UserBrowsingHistoryAdmin(admin.ModelAdmin):
    list_display = ('user', 'trip', 'view_count', 'last_viewed')
    search_fields = ('user__username', 'trip__title')
    list_filter = ('last_viewed',)

@admin.register(ChatMessage)
class ChatMessageAdmin(admin.ModelAdmin):
    list_display = ('user', 'created_at')
    search_fields = ('user__username', 'message', 'response')
    list_filter = ('created_at',)
