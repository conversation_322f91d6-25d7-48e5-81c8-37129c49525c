from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator, MinLengthValidator, MaxLengthValidator, RegexValidator
from encrypted_model_fields.fields import Encry<PERSON><PERSON>har<PERSON><PERSON>
from decimal import Decimal
from django.db.models.signals import post_save
from django.dispatch import receiver
import uuid
import os
from django.utils import timezone

class Country(models.Model):
    CONTINENT_CHOICES = [
        ('AF', 'Afryka'),
        ('AS', 'Azja'),
        ('EU', 'Europa'),
        ('NA', 'Ameryka Północna'),
        ('SA', 'Ameryka Południowa'),
        ('AU', 'Australia'),
        ('AN', 'Antarktyka'),
    ]

    name = models.CharField(max_length=100, unique=True)
    continent = models.CharField(
        max_length=2,
        choices=CONTINENT_CHOICES,
        blank=True,
        null=True,
    )
    description = models.TextField()
    image = models.ImageField(upload_to='countries/')

    def __str__(self):
        return self.name

class Destination(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField()
    country = models.ForeignKey(Country, on_delete=models.CASCADE, related_name='destinations')
    image = models.ImageField(null=True, upload_to='destinations/')

    def __str__(self):
        return f"{self.name}, {self.country.name}"

class Trip(models.Model):
    TRANSPORT_CHOICES = [
        ('plane', 'Samolot'),
        ('bus', 'Autobus'),
        ('train', 'Pociąg'),
    ]

    TRIP_TYPE_CHOICES = [
        ('vacation', 'Wczasy'),
        ('city_break', 'City Break'),
        ('tour', 'Wycieczki objazdowe'),
    ]

    MEAL_PLAN_CHOICES = [
        ('none', 'Brak'),
        ('all_inclusive', 'All Inclusive'),
        ('breakfast', 'Śniadanie'),
        ('half_board', 'Śniadanie + Obiadokolacja'),
        ('full_board', 'Pełne wyżywienie'),
    ]

    title = models.CharField(max_length=200)
    description = models.TextField()
    destination = models.ForeignKey('Destination', on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    available_places = models.PositiveIntegerField()
    transport = models.CharField(max_length=10, choices=TRANSPORT_CHOICES)
    trip_type = models.CharField(  # typ_wakacji
        max_length=20,
        choices=TRIP_TYPE_CHOICES,
        default='vacation',
        verbose_name="Trip type"
    )
    meal_plan = models.CharField(  # wyzywienie
        max_length=20,
        choices=MEAL_PLAN_CHOICES,
        default='none',
        verbose_name="Meal plan"
    )
    is_active = models.BooleanField(default=True)
    image = models.ImageField(upload_to='trips/')

    def __str__(self):
        return self.title

    def is_expired(self):
        """
        Sprawdza, czy data rozpoczęcia wycieczki już minęła.
        """
        import datetime
        return self.start_date < datetime.date.today()

    def update_active_status(self):
        """
        Aktualizuje status aktywności wycieczki na podstawie daty rozpoczęcia.
        Jeśli data rozpoczęcia już minęła, wycieczka staje się nieaktywna.
        Zwraca True, jeśli status został zmieniony, False w przeciwnym razie.
        """
        if self.is_expired() and self.is_active:
            self.is_active = False
            self.save(update_fields=['is_active'])
            return True
        return False

def reservation_document_path(instance, filename):
    # Tworzenie ścieżki zapisu: reservations/reservation_code/filename
    return f'reservations/{instance.reservation_code}/{filename}'

class Reservation(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Oczekująca'),
        ('confirmed', 'Potwierdzona'),
        ('cancelled', 'Anulowana'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    trip = models.ForeignKey('Trip', on_delete=models.CASCADE)
    reservation_code = models.CharField(max_length=10, unique=False, blank=True, editable=False)
    number_of_people = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(default=timezone.now)
    notes = models.TextField(blank=True, null=True)
    reservation_document = models.FileField(upload_to=reservation_document_path, null=True, blank=True)

    def __str__(self):
        return f"Rezerwacja {self.user.username} - {self.trip.title}"

    def save(self, *args, **kwargs):
        if not self.pk and not self.reservation_code:
            self.reservation_code = self._generate_unique_code()
        super().save(*args, **kwargs)

    def _generate_unique_code(self):
        code = str(uuid.uuid4()).upper()[:6]
        while Reservation.objects.filter(reservation_code=code).exists():
            code = str(uuid.uuid4()).upper()[:6]
        return code

class ReservationPerson(models.Model):
    reservation = models.ForeignKey(Reservation, on_delete=models.CASCADE, related_name='people')
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    pesel = EncryptedCharField(
        max_length=11,
        validators=[
            MinLengthValidator(11, message="PESEL musi mieć 11 cyfr."),
            MaxLengthValidator(11, message="PESEL musi mieć 11 cyfr."),
            RegexValidator(r'^\d+$', message="PESEL może zawierać tylko cyfry.")
        ],
        help_text="PESEL musi mieć 11 cyfr."
    )
    is_adult = models.BooleanField(
        default=True,
        help_text="Zaznacz, jeśli osoba jest dorosła."
    )

    def __str__(self):
        person_type = "Dorosły" if self.is_adult else "Dziecko"
        return f"{self.first_name} {self.last_name} ({person_type})"

class Review(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    trip = models.ForeignKey(Trip, on_delete=models.CASCADE)
    rating = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)])
    comment = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'trip')

    def __str__(self):
        return f"Ocena {self.rating}/5 - {self.trip.title}"

class Ticket(models.Model):
    STATUS_CHOICES = [
        ('new', 'Nowe'),
        ('in_progress', 'W realizacji'),
        ('resolved', 'Rozwiązane'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='tickets'
    )
    subject = models.CharField(max_length=200)
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='new'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"[{self.get_status_display()}] {self.subject}"

class Message(models.Model):
    ticket = models.ForeignKey(
        Ticket,
        on_delete=models.CASCADE,
        related_name='messages'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    text = models.TextField(help_text="Treść wiadomości")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.ticket.subject} - {self.created_at}"

class UserPreference(models.Model):
    INTEREST_CHOICES = [
        ('beach', 'Plaże i relaks'),
        ('mountains', 'Góry i trekking'),
        ('city', 'Zwiedzanie miast'),
        ('culture', 'Kultura i zabytki'),
        ('nature', 'Przyroda i parki narodowe'),
    ]

    TRAVEL_STYLE_CHOICES = [
        ('luxury', 'Luksusowe'),
        ('budget', 'Ekonomiczne'),
        ('family', 'Rodzinne'),
        ('solo', 'Podróże solo'),
        ('romantic', 'Romantyczne'),
        ('group', 'Wycieczki grupowe'),
    ]

    CLIMATE_PREFERENCE_CHOICES = [
        ('warm', 'Ciepły klimat'),
        ('cold', 'Chłodny klimat'),
        ('moderate', 'Umiarkowany klimat'),
        ('any', 'Dowolny'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='preferences')
    interests = models.JSONField(default=list, help_text="Lista zainteresowań podróżniczych")
    travel_style = models.JSONField(default=list, help_text="Preferowany styl podróży")
    preferred_continents = models.JSONField(default=list, help_text="Preferowane kontynenty")
    budget_min = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    budget_max = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    climate_preference = models.CharField(max_length=20, choices=CLIMATE_PREFERENCE_CHOICES, default='any')
    preferred_meal_plans = models.JSONField(default=list, help_text="Preferowane plany żywieniowe")
    preferred_transport = models.JSONField(default=list, help_text="Preferowane środki transportu")
    travel_duration_min = models.IntegerField(null=True, blank=True, help_text="Minimalna długość podróży (dni)")
    travel_duration_max = models.IntegerField(null=True, blank=True, help_text="Maksymalna długość podróży (dni)")
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Preferencje użytkownika {self.user.username}"

class UserBrowsingHistory(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='browsing_history')
    trip = models.ForeignKey(Trip, on_delete=models.CASCADE)
    view_count = models.IntegerField(default=1)
    last_viewed = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'trip']
        ordering = ['-last_viewed']

    def __str__(self):
        return f"{self.user.username} - {self.trip.title} ({self.view_count} views)"

@receiver(post_save, sender=User)
def create_user_preferences(sender, instance, created, **kwargs):
    """Create user preferences when a new user is created"""
    if created:
        UserPreference.objects.create(user=instance)

class ChatMessage(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    message = models.TextField()
    response = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Chat with {self.user.username} at {self.created_at}"
