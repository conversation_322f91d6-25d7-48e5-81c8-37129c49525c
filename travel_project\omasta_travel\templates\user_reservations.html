
{% extends 'base.html' %}
{% block title %}Moje rezerwacje - Omasta Travel{% endblock %}

{% block content %}
<h2 class="mb-4"><PERSON><PERSON> rezerwacje</h2>

<div class="table-responsive">
    <table class="table table-striped table-hover table-rounded">
        <thead class="table-custom">
            <tr>
                <th>Wycieczka</th>
                <th>Data wyjazdu</th>
                <th>Ilość osób</th>
                <th>Całkowita cena</th>
                <th>Data rezerwacji</th>
                <th>Status</th>
                <th>Akcje</th>
            </tr>
        </thead>
        <tbody>
            {% for reservation in reservations %}
            <tr>
                <td><a href="{% url 'trip_detail' reservation.trip.pk %}">{{ reservation.trip.title }}</a></td>
                <td>{{ reservation.trip.start_date }}</td>
                <td>{{ reservation.number_of_people }}</td>
                <td>{{ reservation.total_price|floatformat:2 }} zł</td>
                <td>{{ reservation.created_at|date:"d.m.Y H:i" }}</td>
                <td>
                    {% if reservation.status == 'pending' %}
                        <span class="badge bg-warning">Oczekująca</span>
                    {% elif reservation.status == 'confirmed' %}
                        <span class="badge bg-success">Potwierdzona</span>
                    {% else %}
                        <span class="badge bg-danger">Anulowana</span>
                    {% endif %}
                </td>
                <td>
                    <a href="{% url 'reservation_detail' reservation.id %}" 
                       class="btn btn-sm btn-info">Szczegóły</a>
                
                    {% if reservation.status == 'confirmed' and reservation.reservation_document %}
                        <a href="{% url 'download_reservation_pdf' reservation.id %}"
                           class="btn btn-sm btn-warning">
                            Pobierz PDF
                        </a>
                    {% endif %}
                
                    <form method="post" action="{% url 'delete_reservation' reservation.id %}" 
                          style="display: inline;" 
                          onsubmit="return confirm('Czy na pewno chcesz usunąć tę rezerwację?');">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-sm btn-danger">Usuń</button>
                    </form>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="text-center">Nie masz jeszcze żadnych rezerwacji.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
