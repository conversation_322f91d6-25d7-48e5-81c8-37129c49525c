{% extends 'base.html' %}

{% block title %}Panel Administratora - Omasta Travel{% endblock %}

{% block content %}
<h2 class="mb-4">Panel Administratora</h2>

<div class="row">
    <div class="col-md-4 mb-4"> <!-- Dodano mb-4 dla kolumny -->
        <div class="card text-dark bg-warning">
            <div class="card-body">
                <h5 class="text-light">Oczek<PERSON>j<PERSON>ce rezerwacje</h5>
                <p class="text-light display-4">{{ pending_reservations }}</p>
                <a href="{% url 'administrator_pending_reservations' %}" class="btn btn-light">Zarządzaj</a>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4"> <!-- Dodano mb-4 dla kolumny -->
        <div class="card text-dark bg-primary">
            <div class="card-body">
                <h5 class="text-light">Wszystkie rezerwacje</h5>
                <p class="text-light display-4">{{ all_reservations }}</p>
                <a href="{% url 'administrator_all_reservations' %}" class="btn btn-light">Zarządzaj</a>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4"> <!-- Dodano mb-4 dla kolumny -->
        <div class="card text-dark bg-success">
            <div class="card-body">
                <h5 class="text-light">Aktywne wycieczki</h5>
                <p class="text-light display-4">{{ active_trips }}</p>
                <a href="{% url 'admin:omasta_travel_trip_changelist' %}" class="btn btn-light">Zarządzaj</a>
            </div>
        </div>
    </div>
    <!-- Nowa kolumna: Zgłoszenia (CRM) -->
    <div class="col-md-4 mb-4"> <!-- Dodano mb-4 dla kolumny -->
        <div class="card text-dark bg-danger">
            <div class="card-body">
                <h5 class="text-light">Zgłoszenia i wiadomości od klientów</h5>
                <p class="text-light display-4">{{ tickets_count }}</p>
                <a href="{% url 'list_tickets_admin' %}" class="btn btn-light">Zarządzaj</a>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card text-dark bg-info">
            <div class="card-body">
                <h5 class="text-light">Analiza Biznesowa</h5>
                <p class="text-light display-4"><i class="fas fa-chart-line"></i></p>
                <a href="{% url 'business_analysis' %}" class="btn btn-light">Zobacz analizę</a>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Szybkie akcje</h5>
                <div class="list-group">
                    <a href="{% url 'admin:omasta_travel_trip_add' %}" class="list-group-item list-group-item-action">
                        Dodaj nową wycieczkę
                    </a>
                    <a href="{% url 'admin:omasta_travel_destination_add' %}" class="list-group-item list-group-item-action">
                        Dodaj nową destynację
                    </a>
                    <a href="{% url 'admin:omasta_travel_country_add' %}" class="list-group-item list-group-item-action">
                        Dodaj nowy kraj
                    </a>
                    <a href="{% url 'admin:omasta_travel_country_changelist' %}" class="list-group-item list-group-item-action">
                        Zarządzaj krajami
                    </a>
                    <a href="{% url 'admin:omasta_travel_destination_changelist' %}" class="list-group-item list-group-item-action">
                        Zarządzaj destynacjami
                    </a>
                    <a href="{% url 'admin:auth_user_changelist' %}" class="list-group-item list-group-item-action">
                        Zarządzaj użytkownikami
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
