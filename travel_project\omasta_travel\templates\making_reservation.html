{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block content %}
<div class="container py-5">
    <h2 class="mb-4">Rezerwacja - uzupełnij dane uczestników</h2>
    <p class="lead mb-4">Wycieczka: {{ trip.title }} (dla {{ number_of_people }} osób)</p>

    <form method="post">
        {% csrf_token %}
        {{ formset.management_form }}

        <div class="row row-cols-1 row-cols-md-2 g-4">
            {% for form in formset %}
                <div class="col">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="card-title">Dane uczestnika nr {{ forloop.counter }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                {{ form.first_name.label_tag }}
                                {% render_field form.first_name class="form-control" %}
                                {% if form.first_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.first_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                {{ form.last_name.label_tag }}
                                {% render_field form.last_name class="form-control" %}
                                {% if form.last_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.last_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                {{ form.pesel.label_tag }}
                                {% render_field form.pesel class="form-control" %}
                                {% if form.pesel.errors %}
                                    <div class="text-danger">
                                        {% for error in form.pesel.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.is_adult|add_class:"form-check-input" }}
                                    <label class="form-check-label" for="{{ form.is_adult.id_for_label }}">Czy osoba jest pełnoletnia?</label>
                                </div>
                                {% if form.is_adult.errors %}
                                    <div class="text-danger">
                                        {% for error in form.is_adult.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        {% if messages %}
            <ul class="messages mt-3">
                {% for message in messages %}
                    <li{% if message.tags %} class="{{ message.tags }}"{% endif %}>{{ message }}</li>
                {% endfor %}
            </ul>
        {% endif %}
        <div class="mt-3">
            {{ captcha_form.as_p }}
        </div>
        <button type="submit" class="btn btn-success mt-4">Zapisz rezerwację</button>
    </form>
</div>
{% endblock %}
