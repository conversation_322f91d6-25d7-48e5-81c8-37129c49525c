import os
import shutil
import datetime
from django.conf import settings
from google.cloud import storage
from google.oauth2 import service_account
from celery import shared_task

@shared_task
def backup_database():
    try:
        # Create backup directory if it doesn't exist
        backup_dir = os.path.join(settings.BASE_DIR, 'backups')
        os.makedirs(backup_dir, exist_ok=True)

        # Generate backup filename with timestamp
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'db_backup_{timestamp}.sqlite3'
        backup_path = os.path.join(backup_dir, backup_filename)

        # Create database backup
        db_path = str(settings.DATABASES['default']['NAME'])
        shutil.copy2(db_path, backup_path)

        # Initialize Google Cloud Storage client using service account key file
        credentials = service_account.Credentials.from_service_account_file(
            settings.GCS_CREDENTIALS  # This is the path to your key.json file
        )
        storage_client = storage.Client(credentials=credentials, project="omasta-travel")
        bucket = storage_client.bucket(settings.GCS_BUCKET_NAME)

        # Upload backup to Google Cloud Storage
        blob = bucket.blob(f'database_backups/{backup_filename}')
        blob.upload_from_filename(backup_path)

        # Clean up local backup file
        os.remove(backup_path)

        return f'Successfully created and uploaded backup {backup_filename} to Google Cloud Storage'

    except Exception as e:
        return f'Backup failed: {str(e)}'
