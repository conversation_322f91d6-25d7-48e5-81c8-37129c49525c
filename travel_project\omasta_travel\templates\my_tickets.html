{% extends 'base.html' %}
{% block title %}Moje zgłoszenia{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col d-flex justify-content-between align-items-center">
            <h2 class="mb-0">Moje zgłoszenia</h2>
            <a href="{% url 'create_ticket' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Nowe zgłoszenie
            </a>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover table-rounded">
            <thead class="table-custom">
                <tr>
                    <th>ID</th>
                    <th>Temat</th>
                    <th>Wiadomości</th>
                    <th>Data zgłoszenia</th>
                    <th>Status</th>
                    <th>Akcje</th>
                </tr>
            </thead>
            <tbody>
                {% for ticket in tickets %}
                <tr>
                    <td>{{ ticket.id }}</td>
                    <td>{{ ticket.subject }}</td>
                    <td>{{ ticket.messages.count }}</td>
                    <td>{{ ticket.created_at|date:"d.m.Y H:i" }}</td>
                    <td>
                        {% if ticket.status == 'new' %}
                            <span class="badge bg-primary">Nowe</span>
                        {% elif ticket.status == 'in_progress' %}
                            <span class="badge bg-warning">W realizacji</span>
                        {% elif ticket.status == 'closed' %}
                            <span class="badge bg-success">Zamknięte</span>
                        {% else %}
                            <span class="badge bg-secondary">{{ ticket.get_status_display }}</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{% url 'ticket_detail' ticket.id %}" class="btn btn-sm btn-info">
                                <i class="bi bi-eye"></i> Szczegóły
                            </a>
                            <a href="{% url 'delete_ticket' ticket.id %}" class="btn btn-sm btn-danger" onclick="return confirm('Czy na pewno chcesz usunąć to zgłoszenie?');">
                                <i class="bi bi-trash"></i> Usuń
                            </a>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="text-center">
                        <p class="my-3">Nie masz jeszcze żadnych zgłoszeń.</p>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
