{% extends 'base.html' %}
{% load static %}

{% block title %}Chatbot Omasta Travel{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Chatbot Omasta Travel</h4>
                    {% if user.is_authenticated %}
                    <button id="clearChatBtn" class="btn btn-sm btn-light">Wyczyść historię</button>
                    {% endif %}
                </div>
                <div class="card-body">
                    <div id="chatMessages" class="chat-messages mb-3" style="height: 400px; overflow-y: auto;">
                        <div class="message-container">
                            <div class="message bot-message">
                                <div class="message-content">
                                    <p>Witaj w Omasta Travel! Jestem Twoim wirtualnym asystentem. W czym mogę Ci pomóc?</p>
                                    <p>Możesz zapytać mnie o:</p>
                                    <ul>
                                        <li>Ofertę wycieczek do różnych krajów</li>
                                        <li>Dostępne destynacje i kierunki</li>
                                        <li>Ceny i terminy wyjazdów</li>
                                        <li>Ogólne informacje o podróżowaniu</li>
                                        {% if user.is_authenticated %}
                                        <li>Status Twoich rezerwacji</li>
                                        {% else %}
                                        <li>Zaloguj się, aby sprawdzić status Twoich rezerwacji</li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        {% if user.is_authenticated and chat_history %}
                            {% for message in chat_history %}
                            <div class="message-container">
                                <div class="message user-message">
                                    <div class="message-content">
                                        <p>{{ message.message }}</p>
                                        <small class="text-muted">{{ message.created_at|date:"d.m.Y H:i" }}</small>
                                    </div>
                                </div>
                                <div class="message bot-message">
                                    <div class="message-content">
                                        <p>{{ message.response|linebreaksbr }}</p>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% endif %}
                    </div>
                    <form id="chatForm" class="d-flex">
                        <input type="text" id="userQuestion" class="form-control me-2" placeholder="Napisz wiadomość..." required>
                        <button type="submit" class="btn btn-primary">Wyślij</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .chat-messages {
        display: flex;
        flex-direction: column;
    }
    .message-container {
        display: flex;
        flex-direction: column;
        margin-bottom: 15px;
    }
    .message {
        max-width: 80%;
        padding: 10px 15px;
        border-radius: 15px;
        margin-bottom: 5px;
    }
    .user-message {
        align-self: flex-end;
        background-color: #dcf8c6;
        border-top-right-radius: 0;
    }
    .bot-message {
        align-self: flex-start;
        background-color: #f0f0f0;
        border-top-left-radius: 0;
    }
    .message-content p {
        margin-bottom: 0.5rem;
    }
    .message-content p:last-child {
        margin-bottom: 0;
    }
    .message-content ul {
        padding-left: 20px;
        margin-bottom: 0;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const chatForm = document.getElementById('chatForm');
        const userQuestion = document.getElementById('userQuestion');
        const chatMessages = document.getElementById('chatMessages');
        const clearChatBtn = document.getElementById('clearChatBtn');
        
        // Przewiń do ostatniej wiadomości
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        // Obsługa formularza
        chatForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const question = userQuestion.value.trim();
            if (!question) return;
            
            // Dodaj wiadomość użytkownika do czatu
            addUserMessage(question);
            
            // Wyczyść pole wejściowe
            userQuestion.value = '';
            
            // Dodaj wskaźnik ładowania
            const loadingIndicator = addLoadingIndicator();
            
            // Wyślij zapytanie do serwera
            sendQuestion(question, loadingIndicator);
        });
        
        // Funkcja do wysyłania zapytania
        function sendQuestion(question, loadingIndicator) {
            const formData = new FormData();
            formData.append('question', question);
            
            fetch('{% url "ask_question" %}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                // Usuń wskaźnik ładowania
                loadingIndicator.remove();
                
                if (data.status === 'success') {
                    // Dodaj odpowiedź bota
                    addBotMessage(data.response);
                } else {
                    // Obsługa błędu
                    addBotMessage('Przepraszam, wystąpił błąd. Proszę spróbować ponownie.');
                }
            })
            .catch(error => {
                // Usuń wskaźnik ładowania
                loadingIndicator.remove();
                
                // Obsługa błędu
                addBotMessage('Przepraszam, wystąpił błąd połączenia. Proszę spróbować ponownie.');
                console.error('Error:', error);
            });
        }
        
        // Funkcja do dodawania wiadomości użytkownika
        function addUserMessage(message) {
            const messageContainer = document.createElement('div');
            messageContainer.className = 'message-container';
            
            const messageElement = document.createElement('div');
            messageElement.className = 'message user-message';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.innerHTML = `<p>${message}</p>`;
            
            messageElement.appendChild(messageContent);
            messageContainer.appendChild(messageElement);
            chatMessages.appendChild(messageContainer);
            
            // Przewiń do końca
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            return messageContainer;
        }
        
        // Funkcja do dodawania wiadomości bota
        function addBotMessage(message) {
            const lastContainer = document.querySelector('.message-container:last-child');
            
            const messageElement = document.createElement('div');
            messageElement.className = 'message bot-message';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.innerHTML = `<p>${message.replace(/\n/g, '</p><p>')}</p>`;
            
            messageElement.appendChild(messageContent);
            lastContainer.appendChild(messageElement);
            
            // Przewiń do końca
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // Funkcja do dodawania wskaźnika ładowania
        function addLoadingIndicator() {
            const lastContainer = document.querySelector('.message-container:last-child');
            
            const loadingElement = document.createElement('div');
            loadingElement.className = 'message bot-message loading';
            loadingElement.innerHTML = `<div class="spinner-grow spinner-grow-sm text-primary" role="status">
                                           <span class="visually-hidden">Ładowanie...</span>
                                         </div>`;
            
            lastContainer.appendChild(loadingElement);
            
            // Przewiń do końca
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            return loadingElement;
        }
        
        // Obsługa przycisku do czyszczenia historii czatu
        if (clearChatBtn) {
            clearChatBtn.addEventListener('click', function() {
                if (confirm('Czy na pewno chcesz wyczyścić całą historię czatu?')) {
                    fetch('{% url "clear_chat_history" %}', {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // Wyczyść czat oprócz wiadomości powitalnej
                            const welcomeMessage = chatMessages.querySelector('.message-container:first-child');
                            chatMessages.innerHTML = '';
                            chatMessages.appendChild(welcomeMessage);
                        } else {
                            alert('Nie udało się wyczyścić historii czatu.');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Wystąpił błąd podczas czyszczenia historii czatu.');
                    });
                }
            });
        }
        
        // Funkcja do pobierania ciasteczek CSRF
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}



















