import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Używamy Agg jako backend, aby dzia<PERSON><PERSON>o bez GUI
import seaborn as sns
import io
import base64
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from django.db.models import Count, Sum, Avg, F, Q
from django.db.models.functions import TruncMonth, TruncYear, ExtractMonth, ExtractYear
from django.utils import timezone
import google.generativeai as genai
from django.conf import settings
import json

from .models import (
    Trip,
    Reservation,
    ReservationPerson,
    Ticket,
    Review,
    Country,
    Destination,
    UserPreference,
    UserBrowsingHistory,
    User
)
from collections import Counter

# Konfiguracja API Gemini
genai.configure(api_key=settings.GEMINI_API_KEY)

def get_gemini_model():
    return genai.GenerativeModel('gemini-2.0-flash')

def get_monthly_sales_data(months=None):
    """
    Pobiera dane o sprzedaży miesięcznej.
    Jeśli months=None, pobiera dane dla wszystkich miesięcy, w któ<PERSON>ch były rezerwacje.
    W przeciwnym razie pobiera dane z ostatnich X miesięcy.
    Uwzględnia wszystkie miesiące w zakresie dat, nawet te bez rezerwacji (z wartościami 0).
    """
    # Określ zakres dat
    end_date = timezone.now().date()

    if months:
        # Pobierz dane z ostatnich X miesięcy
        start_date = end_date - relativedelta(months=months)
    else:
        # Jeśli nie podano liczby miesięcy, znajdź datę pierwszej rezerwacji
        first_reservation = Reservation.objects.order_by('created_at').first()
        if first_reservation:
            start_date = first_reservation.created_at.date().replace(day=1)  # Pierwszy dzień miesiąca pierwszej rezerwacji
        else:
            # Jeśli nie ma rezerwacji, zwróć pusty DataFrame
            return pd.DataFrame(columns=['month', 'count', 'total_revenue'])

    # Pobierz rezerwacje z określonego okresu
    reservations = Reservation.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    ).annotate(
        month=TruncMonth('created_at')
    ).values('month').annotate(
        count=Count('id'),
        total_revenue=Sum('total_price')
    ).order_by('month')

    # Konwersja do DataFrame
    df = pd.DataFrame(list(reservations))

    # Jeśli nie ma danych, zwróć pusty DataFrame
    if df.empty:
        return pd.DataFrame(columns=['month', 'count', 'total_revenue'])

    # Konwertuj kolumnę 'month' na format datetime bez strefy czasowej
    df['month'] = pd.to_datetime(df['month']).dt.date

    # Generuj wszystkie miesiące w zakresie dat
    all_months = []
    current_date = start_date.replace(day=1)  # Pierwszy dzień miesiąca

    while current_date <= end_date:
        all_months.append(current_date)
        current_date = (current_date + relativedelta(months=1))

    # Utwórz DataFrame ze wszystkimi miesiącami
    all_months_df = pd.DataFrame({'month': all_months})

    # Alternatywne podejście - zamiast merge, użyjmy reindex
    # Utwórz indeks z wszystkich miesięcy
    month_index = pd.DatetimeIndex([pd.Timestamp(d) for d in all_months])

    # Utwórz nowy DataFrame z indeksem miesięcy
    result_df = pd.DataFrame(index=month_index, columns=['count', 'total_revenue'])

    # Dla każdego wiersza w oryginalnym DataFrame, dodaj dane do odpowiedniego miesiąca
    for _, row in df.iterrows():
        month_key = pd.Timestamp(row['month'])
        result_df.loc[month_key, 'count'] = row['count']
        result_df.loc[month_key, 'total_revenue'] = row['total_revenue']

    # Wypełnij brakujące wartości zerami
    result_df = result_df.fillna(0)

    # Resetuj indeks, aby 'month' stał się kolumną
    result_df = result_df.reset_index()
    result_df = result_df.rename(columns={'index': 'month'})

    return result_df

def get_yearly_sales_data(years=3):
    """
    Pobiera dane o sprzedaży rocznej z ostatnich X lat
    """
    end_date = timezone.now().date()
    start_date = end_date - relativedelta(years=years)

    # Pobierz rezerwacje z określonego okresu
    reservations = Reservation.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    ).annotate(
        year=TruncYear('created_at')
    ).values('year').annotate(
        count=Count('id'),
        total_revenue=Sum('total_price')
    ).order_by('year')

    # Konwersja do DataFrame
    df = pd.DataFrame(list(reservations))

    return df

def get_top_destinations(limit=10):
    """
    Pobiera najpopularniejsze kierunki podróży
    """
    top_destinations = Reservation.objects.values(
        'trip__destination__name',
        'trip__destination__country__name'
    ).annotate(
        count=Count('id'),
        revenue=Sum('total_price')
    ).order_by('-count')[:limit]

    destinations_list = list(top_destinations)

    return destinations_list

def get_top_rated_trips(limit=5):
    """
    Pobiera najlepiej oceniane wycieczki
    """
    try:
        # Pobierz wycieczki z ocenami
        top_trips = Trip.objects.filter(
            review__isnull=False  # Tylko wycieczki, które mają oceny
        ).annotate(
            avg_rating=Avg('review__rating'),  # Średnia ocena
            reviews_count=Count('review')      # Liczba ocen
        ).filter(
            reviews_count__gte=1  # Przynajmniej jedna ocena
        ).order_by(
            '-avg_rating',        # Sortuj po średniej ocenie (malejąco)
            '-reviews_count'      # Przy równych ocenach, sortuj po liczbie ocen
        )[:limit]

        # Przygotuj dane do zwrócenia
        result = []
        for trip in top_trips:
            result.append({
                'id': trip.id,
                'title': trip.title,
                'destination': f"{trip.destination.name}, {trip.destination.country.name}",
                'avg_rating': trip.avg_rating,
                'reviews_count': trip.reviews_count,
                'price': trip.price
            })

        return result
    except Exception as e:
        print(f"Błąd podczas pobierania najlepiej ocenianych wycieczek: {e}")
        return []

def get_customer_service_data():
    """
    Pobiera dane o obsłudze klienta (zgłoszenia, bilety)
    """
    try:
        # Zgłoszenia według statusu
        tickets_by_status = Ticket.objects.values('status').annotate(
            count=Count('id')
        ).order_by('status')

        total_tickets = Ticket.objects.count()
        open_tickets = Ticket.objects.filter(status='open').count()
        closed_tickets = Ticket.objects.filter(status='closed').count()

        # Zgłoszenia z dzisiejszego dnia
        today = timezone.now().date()
        today_tickets = Ticket.objects.filter(created_at__date=today).count()

        # Zgłoszenia z bieżącego miesiąca
        current_month_start = today.replace(day=1)
        current_month_tickets = Ticket.objects.filter(
            created_at__date__gte=current_month_start,
            created_at__date__lte=today
        ).count()

        # Zgłoszenia z bieżącego roku
        current_year_start = today.replace(month=1, day=1)
        current_year_tickets = Ticket.objects.filter(
            created_at__date__gte=current_year_start,
            created_at__date__lte=today
        ).count()

        return {
            'tickets_by_status': list(tickets_by_status),
            'total_tickets': total_tickets,
            'open_tickets': open_tickets,
            'closed_tickets': closed_tickets,
            'today_tickets': today_tickets,
            'current_month_tickets': current_month_tickets,
            'current_year_tickets': current_year_tickets
        }
    except Exception as e:
        print(f"Błąd podczas pobierania danych o obsłudze klienta: {e}")
        return {
            'tickets_by_status': [],
            'total_tickets': 0,
            'open_tickets': 0,
            'closed_tickets': 0,
            'today_tickets': 0,
            'current_month_tickets': 0,
            'current_year_tickets': 0
        }

def get_reservation_stats():
    """
    Pobiera statystyki rezerwacji
    """
    try:
        # Ogólne statystyki
        total_reservations = Reservation.objects.count()
        confirmed_reservations = Reservation.objects.filter(status='confirmed').count()
        cancelled_reservations = Reservation.objects.filter(status='cancelled').count()
        pending_reservations = Reservation.objects.filter(status='pending').count()

        # Średnia wartość rezerwacji
        avg_reservation_value = Reservation.objects.aggregate(
            avg_value=Avg('total_price')
        )['avg_value'] or 0

        # Całkowity przychód
        total_revenue = Reservation.objects.filter(
            status='confirmed'
        ).aggregate(
            total=Sum('total_price')
        )['total'] or 0

        # Statystyki dla dzisiejszego dnia
        today = timezone.now().date()
        today_reservations = Reservation.objects.filter(created_at__date=today).count()
        today_revenue = Reservation.objects.filter(
            created_at__date=today,
            status='confirmed'
        ).aggregate(
            total=Sum('total_price')
        )['total'] or 0

        # Statystyki dla bieżącego miesiąca
        current_month_start = today.replace(day=1)
        current_month_reservations = Reservation.objects.filter(
            created_at__date__gte=current_month_start,
            created_at__date__lte=today
        ).count()
        current_month_revenue = Reservation.objects.filter(
            created_at__date__gte=current_month_start,
            created_at__date__lte=today,
            status='confirmed'
        ).aggregate(
            total=Sum('total_price')
        )['total'] or 0

        # Statystyki dla bieżącego roku
        current_year_start = today.replace(month=1, day=1)
        current_year_reservations = Reservation.objects.filter(
            created_at__date__gte=current_year_start,
            created_at__date__lte=today
        ).count()
        current_year_revenue = Reservation.objects.filter(
            created_at__date__gte=current_year_start,
            created_at__date__lte=today,
            status='confirmed'
        ).aggregate(
            total=Sum('total_price')
        )['total'] or 0

        return {
            'total_reservations': total_reservations,
            'confirmed_reservations': confirmed_reservations,
            'cancelled_reservations': cancelled_reservations,
            'pending_reservations': pending_reservations,
            'avg_reservation_value': avg_reservation_value,
            'total_revenue': total_revenue,
            'today_reservations': today_reservations,
            'today_revenue': today_revenue,
            'current_month_reservations': current_month_reservations,
            'current_month_revenue': current_month_revenue,
            'current_year_reservations': current_year_reservations,
            'current_year_revenue': current_year_revenue
        }
    except Exception as e:
        print(f"Błąd podczas pobierania statystyk rezerwacji: {e}")
        return {
            'total_reservations': 0,
            'confirmed_reservations': 0,
            'cancelled_reservations': 0,
            'pending_reservations': 0,
            'avg_reservation_value': 0,
            'total_revenue': 0,
            'today_reservations': 0,
            'today_revenue': 0,
            'current_month_reservations': 0,
            'current_month_revenue': 0,
            'current_year_reservations': 0,
            'current_year_revenue': 0
        }

def generate_monthly_sales_chart():
    """
    Generuje wykres sprzedaży miesięcznej dla wszystkich miesięcy, w których były rezerwacje
    """
    try:
        # Pobierz dane dla wszystkich miesięcy
        df = get_monthly_sales_data(months=None)

        if df.empty or 'month' not in df.columns:
            # Jeśli nie ma danych, zwróć None
            print("Brak danych do wykresu miesięcznego")
            return None

        # Formatowanie dat na osi X
        if 'month' in df.columns and not df['month'].isna().all():
            df['month_str'] = df['month'].dt.strftime('%b %Y')
        else:
            # Jeśli nie ma dat, użyj indeksów jako etykiet
            df['month_str'] = [f'Miesiąc {i+1}' for i in range(len(df))]

        # Wyczyść poprzednie wykresy
        plt.clf()
        plt.close('all')

        # Utwórz nowy wykres
        plt.figure(figsize=(12, 6))

        # Wykres słupkowy liczby rezerwacji
        ax1 = plt.subplot(111)
        bars = ax1.bar(df['month_str'], df['count'], color='skyblue')
        ax1.set_xlabel('Miesiąc')
        ax1.set_ylabel('Liczba rezerwacji', color='skyblue')
        ax1.tick_params(axis='y', labelcolor='skyblue')

        # Dodanie etykiet z wartościami
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{int(height)}', ha='center', va='bottom')

        # Wykres liniowy przychodu
        ax2 = ax1.twinx()
        ax2.plot(df['month_str'], df['total_revenue'], color='red', marker='o', linestyle='-', linewidth=2, markersize=6)
        ax2.set_ylabel('Przychód (PLN)', color='red')
        ax2.tick_params(axis='y', labelcolor='red')

        # Dodanie etykiet z wartościami
        for i, value in enumerate(df['total_revenue']):
            # Konwersja Decimal do float
            value_float = float(value)
            ax2.text(i, value_float + 50, f'{int(value_float)} PLN', ha='center', va='bottom', color='red')

        plt.title('Miesięczna sprzedaż i przychód (wszystkie miesiące)')
        plt.xticks(rotation=45)
        plt.tight_layout()

        # Zapisz wykres do bufora
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png')
        buffer.seek(0)
        image_png = buffer.getvalue()
        buffer.close()

        # Konwersja do base64
        graphic = base64.b64encode(image_png).decode('utf-8')

        return graphic
    except Exception as e:
        print(f"Błąd podczas generowania wykresu sprzedaży miesięcznej: {e}")
        return None

def generate_top_destinations_chart(limit=5):
    """
    Generuje wykres najpopularniejszych kierunków z informacją o liczbie rezerwacji i przychodach
    """
    try:
        top_destinations = get_top_destinations(limit)

        if not top_destinations:
            return None

        df = pd.DataFrame(top_destinations)

        # Wyczyść poprzednie wykresy
        plt.clf()
        plt.close('all')

        # Utwórz nowy wykres
        fig, ax1 = plt.subplots(figsize=(12, 7))

        # Tworzenie etykiet dla wykresu
        labels = [f"{dest['trip__destination__name']} ({dest['trip__destination__country__name']})"
                for dest in top_destinations]

        # Wykres słupkowy dla liczby rezerwacji
        x = np.arange(len(labels))
        width = 0.35  # szerokość słupków

        bars1 = ax1.bar(x - width/2, df['count'], width, color='skyblue', label='Liczba rezerwacji')
        ax1.set_xlabel('Destynacja')
        ax1.set_ylabel('Liczba rezerwacji', color='skyblue')
        ax1.tick_params(axis='y', labelcolor='skyblue')

        # Dodanie etykiet z wartościami dla liczby rezerwacji
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{int(height)}', ha='center', va='bottom', color='skyblue')

        # Drugi wykres (oś Y) dla przychodów
        ax2 = ax1.twinx()
        bars2 = ax2.bar(x + width/2, df['revenue'], width, color='salmon', label='Przychód (PLN)')
        ax2.set_ylabel('Przychód (PLN)', color='salmon')
        ax2.tick_params(axis='y', labelcolor='salmon')

        # Dodanie etykiet z wartościami dla przychodów
        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{int(height)} PLN', ha='center', va='bottom', color='salmon')

        # Ustawienie etykiet osi X
        ax1.set_xticks(x)
        ax1.set_xticklabels(labels, rotation=45, ha='right')

        # Dodanie legendy
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

        plt.title('Najpopularniejsze kierunki podróży - liczba rezerwacji i przychód')
        plt.tight_layout()

        # Zapisz wykres do bufora
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png')
        buffer.seek(0)
        image_png = buffer.getvalue()
        buffer.close()

        # Konwersja do base64
        graphic = base64.b64encode(image_png).decode('utf-8')

        return graphic
    except Exception as e:
        print(f"Błąd podczas generowania wykresu najpopularniejszych kierunków: {e}")
        return None

def generate_customer_service_chart():
    """
    Generuje wykres danych obsługi klienta
    """
    try:
        service_data = get_customer_service_data()

        if not service_data['tickets_by_status']:
            return None

        # Wyczyść poprzednie wykresy
        plt.clf()
        plt.close('all')

        plt.figure(figsize=(10, 6))

        # Dane do wykresu kołowego
        labels = [item['status'] for item in service_data['tickets_by_status']]
        sizes = [item['count'] for item in service_data['tickets_by_status']]

        # Wykres kołowy
        plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90,
                colors=sns.color_palette("pastel", len(labels)))
        plt.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle
        plt.title('Zgłoszenia według statusu')
        plt.tight_layout()

        # Zapisz wykres do bufora
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png')
        buffer.seek(0)
        image_png = buffer.getvalue()
        buffer.close()

        # Konwersja do base64
        graphic = base64.b64encode(image_png).decode('utf-8')

        return graphic
    except Exception as e:
        print(f"Błąd podczas generowania wykresu obsługi klienta: {e}")
        return None

def generate_top_rated_trips_chart():
    """
    Generuje wykres najlepiej ocenianych wycieczek
    """
    try:
        top_trips = get_top_rated_trips(5)  # Pobierz top 5 najlepiej ocenianych wycieczek

        if not top_trips:
            return None

        # Konwersja do DataFrame
        df = pd.DataFrame(top_trips)

        # Wyczyść poprzednie wykresy
        plt.clf()
        plt.close('all')

        # Utwórz nowy wykres
        fig, ax = plt.subplots(figsize=(12, 7))

        # Przygotuj dane do wykresu
        trip_names = [trip['title'][:25] + '...' if len(trip['title']) > 25 else trip['title'] for trip in top_trips]
        avg_ratings = [trip['avg_rating'] for trip in top_trips]
        reviews_count = [trip['reviews_count'] for trip in top_trips]

        # Utwórz kolorowy gradient dla słupków w zależności od oceny
        colors = plt.cm.RdYlGn(np.linspace(0.2, 0.8, len(trip_names)))

        # Wykres słupkowy dla średnich ocen
        bars = ax.bar(trip_names, avg_ratings, color=colors, alpha=0.8)

        # Dodaj etykiety z wartościami
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                   f'{avg_ratings[i]:.1f} ★', ha='center', va='bottom', fontweight='bold')

            # Dodaj liczbę ocen pod nazwą wycieczki
            ax.text(bar.get_x() + bar.get_width()/2., -0.15,
                   f'({reviews_count[i]} ocen)', ha='center', va='top', fontsize=9, color='gray')

        # Dodaj linie pomocnicze dla lepszej czytelności
        ax.grid(axis='y', linestyle='--', alpha=0.7)

        # Ustaw zakres osi Y od 0 do 5 (maksymalna ocena)
        ax.set_ylim(0, 5.2)

        # Dodaj etykiety osi i tytuł
        ax.set_xlabel('Wycieczka')
        ax.set_ylabel('Średnia ocena (1-5 ★)')
        ax.set_title('Top 5 najlepiej ocenianych wycieczek', fontsize=14, fontweight='bold')

        # Obróć etykiety osi X dla lepszej czytelności
        plt.xticks(rotation=30, ha='right')

        # Dodaj adnotację z wyjaśnieniem
        plt.figtext(0.5, 0.01, 'Liczba ocen podana w nawiasach pod nazwą wycieczki',
                   ha='center', fontsize=9, style='italic')

        plt.tight_layout()

        # Zapisz wykres do bufora
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png')
        buffer.seek(0)
        image_png = buffer.getvalue()
        buffer.close()

        # Konwersja do base64
        graphic = base64.b64encode(image_png).decode('utf-8')

        return graphic
    except Exception as e:
        print(f"Błąd podczas generowania wykresu najlepiej ocenianych wycieczek: {e}")
        return None

def generate_ai_insights():
    """
    Generuje wnioski i rekomendacje przy użyciu AI (Google Gemini)
    """
    try:
        model = get_gemini_model()

        # Przygotuj dane do analizy
        monthly_sales = get_monthly_sales_data()
        yearly_sales = get_yearly_sales_data()
        top_destinations = get_top_destinations()
        customer_service = get_customer_service_data()
        reservation_stats = get_reservation_stats()

        # Sprawdź, czy mamy wystarczająco danych do analizy
        if (monthly_sales.empty and yearly_sales.empty and
            not top_destinations and
            not customer_service['tickets_by_status']):
            return """
            Raport Analizy Biznesowej Biura Podróży

            Podsumowanie wyników:
            Aktualnie brak danych do przeprowadzenia analizy biznesowej.
            Dodaj rezerwacje i zgłoszenia do systemu, aby umożliwić analizę.

            Kluczowe wnioski:
            • Brak danych w systemie
            • Potrzeba dodania rezerwacji i zgłoszeń

            Rekomendacje:
            • Dodaj rezerwacje do systemu
            • Dodaj zgłoszenia do systemu
            • Wróć do analizy po dodaniu danych
            """

        # Konwersja danych do formatu JSON
        analysis_data = {
            'monthly_sales': monthly_sales.to_dict('records') if not monthly_sales.empty else [],
            'yearly_sales': yearly_sales.to_dict('records') if not yearly_sales.empty else [],
            'top_destinations': top_destinations,
            'customer_service': customer_service,
            'reservation_stats': reservation_stats
        }

        # Prompt dla modelu AI
        prompt = f"""
        Jako ekspert ds. analizy biznesowej biura podróży, przeanalizuj poniższe dane i przygotuj krótki raport
        zawierający kluczowe wnioski i rekomendacje. Raport powinien być napisany w języku polskim i zawierać
        maksymalnie 5 najważniejszych wniosków oraz 3-5 konkretnych rekomendacji biznesowych.

        DANE DO ANALIZY:
        {json.dumps(analysis_data, indent=2, default=str)}

        Twój raport powinien zawierać:
        1. Krótkie podsumowanie wyników finansowych
        2. Analiza trendów sprzedażowych
        3. Analiza popularności kierunków
        4. Ocena obsługi klienta
        5. Konkretne rekomendacje biznesowe

        Format raportu:
        - Tytuł: "Raport Analizy Biznesowej Biura Podróży"
        - Sekcja 1: Podsumowanie wyników
        - Sekcja 2: Kluczowe wnioski (punkty)
        - Sekcja 3: Rekomendacje (punkty)

        Pisz zwięźle i konkretnie, skupiając się na najważniejszych informacjach.
        Jeśli danych jest mało, zaproponuj sposoby na ich lepsze gromadzenie i wykorzystanie.
        """

        try:
            response = model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"Błąd podczas generowania raportu AI: {e}")
            return """
            Raport Analizy Biznesowej Biura Podróży

            Podsumowanie wyników:
            Nie udało się wygenerować automatycznego raportu AI. Poniżej przedstawiamy podstawowe wnioski
            na podstawie dostępnych danych.

            Kluczowe wnioski:
            • Całkowity przychód: {reservation_stats['total_revenue']} PLN
            • Średnia wartość rezerwacji: {reservation_stats['avg_reservation_value']} PLN
            • Liczba rezerwacji: {reservation_stats['total_reservations']}

            Rekomendacje:
            • Rozbudowa systemu analitycznego
            • Regularne monitorowanie kluczowych wskaźników biznesowych
            • Wdrożenie strategii zwiększania średniej wartości rezerwacji
            """
    except Exception as e:
        print(f"Błąd podczas przygotowywania danych do analizy AI: {e}")
        return "Nie udało się wygenerować raportu AI. Spróbuj ponownie później."

def get_user_preferences_data():
    """
    Pobiera dane o preferencjach użytkowników
    """
    try:
        # Pobierz wszystkie preferencje użytkowników
        preferences = UserPreference.objects.all()

        if not preferences.exists():
            return None

        # Analizuj preferencje
        interests_counter = Counter()
        travel_style_counter = Counter()
        climate_preference_counter = Counter()
        transport_counter = Counter()
        meal_plans_counter = Counter()
        continents_counter = Counter()

        for pref in preferences:
            # Dodaj preferencje do odpowiednich liczników
            if isinstance(pref.interests, list):
                interests_counter.update(pref.interests)

            if isinstance(pref.travel_style, list):
                travel_style_counter.update(pref.travel_style)

            if pref.climate_preference:
                climate_preference_counter[pref.climate_preference] += 1

            if isinstance(pref.preferred_transport, list):
                transport_counter.update(pref.preferred_transport)

            if isinstance(pref.preferred_meal_plans, list):
                meal_plans_counter.update(pref.preferred_meal_plans)

            if isinstance(pref.preferred_continents, list):
                continents_counter.update(pref.preferred_continents)

        # Przygotuj dane do zwrócenia
        return {
            'interests': dict(interests_counter.most_common()),
            'travel_style': dict(travel_style_counter.most_common()),
            'climate_preference': dict(climate_preference_counter.most_common()),
            'transport': dict(transport_counter.most_common()),
            'meal_plans': dict(meal_plans_counter.most_common()),
            'continents': dict(continents_counter.most_common()),
            'total_users_with_preferences': preferences.count()
        }
    except Exception as e:
        print(f"Błąd podczas pobierania danych o preferencjach użytkowników: {e}")
        return None

def get_conversion_data():
    """
    Pobiera dane o konwersji (stosunek przeglądania do rezerwacji)
    """
    try:
        # Pobierz dane o przeglądaniu wycieczek
        views_data = UserBrowsingHistory.objects.values('trip').annotate(
            total_views=Sum('view_count')
        )

        # Pobierz dane o rezerwacjach
        reservations_data = Reservation.objects.values('trip').annotate(
            total_reservations=Count('id')
        )

        # Przygotuj słownik z danymi o przeglądaniu
        views_dict = {item['trip']: item['total_views'] for item in views_data}

        # Przygotuj słownik z danymi o rezerwacjach
        reservations_dict = {item['trip']: item['total_reservations'] for item in reservations_data}

        # Pobierz wszystkie wycieczki
        trips = Trip.objects.all()

        # Przygotuj dane o konwersji dla każdej wycieczki
        conversion_data = []
        for trip in trips:
            views = views_dict.get(trip.id, 0)
            reservations = reservations_dict.get(trip.id, 0)

            # Oblicz współczynnik konwersji (jeśli są jakieś wyświetlenia)
            conversion_rate = (reservations / views * 100) if views > 0 else 0

            conversion_data.append({
                'trip_id': trip.id,
                'trip_title': trip.title,
                'views': views,
                'reservations': reservations,
                'conversion_rate': conversion_rate
            })

        # Posortuj dane według współczynnika konwersji (malejąco)
        conversion_data.sort(key=lambda x: x['conversion_rate'], reverse=True)

        # Oblicz ogólny współczynnik konwersji
        total_views = sum(views_dict.values())
        total_reservations = sum(reservations_dict.values())
        overall_conversion_rate = (total_reservations / total_views * 100) if total_views > 0 else 0

        return {
            'trips': conversion_data,
            'total_views': total_views,
            'total_reservations': total_reservations,
            'overall_conversion_rate': overall_conversion_rate
        }
    except Exception as e:
        print(f"Błąd podczas pobierania danych o konwersji: {e}")
        return None

def generate_user_preferences_chart():
    """
    Generuje wykres preferencji użytkowników
    """
    try:
        preferences_data = get_user_preferences_data()

        if not preferences_data:
            return None

        # Wyczyść poprzednie wykresy
        plt.clf()
        plt.close('all')

        # Utwórz wykres z 2x2 podwykresami
        fig, axs = plt.subplots(2, 2, figsize=(12, 10))

        # Słownik tłumaczeń dla zainteresowań
        interest_translations = {
            'beach': 'Plaża',
            'mountains': 'Góry',
            'city': 'Zwiedzanie miast',
            'culture': 'Kultura',
            'food': 'Kulinaria',
            'adventure': 'Przygoda',
            'relax': 'Wypoczynek',
            'nature': 'Natura',
            'history': 'Historia',
            'sports': 'Sport'
        }

        # Słownik tłumaczeń dla stylu podróży
        style_translations = {
            'luxury': 'Luksusowy',
            'budget': 'Ekonomiczny',
            'family': 'Rodzinny',
            'solo': 'Indywidualny',
            'group': 'Grupowy',
            'romantic': 'Romantyczny',
            'adventure': 'Przygodowy',
            'cultural': 'Kulturowy'
        }

        # Słownik tłumaczeń dla transportu
        transport_translations = {
            'plane': 'Samolot',
            'train': 'Pociąg',
            'bus': 'Autobus',
            'car': 'Samochód',
            'cruise': 'Rejs',
            'bike': 'Rower',
            'walking': 'Pieszy'
        }

        # Słownik tłumaczeń dla klimatu
        climate_translations = {
            'hot': 'Gorący',
            'mild': 'Umiarkowany',
            'cold': 'Chłodny',
            'tropical': 'Tropikalny',
            'dry': 'Suchy',
            'humid': 'Wilgotny'
        }

        # 1. Wykres zainteresowań
        interests = preferences_data['interests']
        if interests:
            # Tłumaczenie kluczy
            translated_interests = {interest_translations.get(k, k): v for k, v in interests.items()}

            axs[0, 0].bar(translated_interests.keys(), translated_interests.values(), color=sns.color_palette("viridis", len(translated_interests)))
            axs[0, 0].set_title('Zainteresowania podróżnicze')
            axs[0, 0].set_xlabel('Typ zainteresowania')
            axs[0, 0].set_ylabel('Liczba użytkowników')
            axs[0, 0].tick_params(axis='x', rotation=45)

        # 2. Wykres stylu podróży
        travel_style = preferences_data['travel_style']
        if travel_style:
            # Tłumaczenie kluczy
            translated_style = {style_translations.get(k, k): v for k, v in travel_style.items()}

            axs[0, 1].bar(translated_style.keys(), translated_style.values(), color=sns.color_palette("magma", len(translated_style)))
            axs[0, 1].set_title('Preferowany styl podróży')
            axs[0, 1].set_xlabel('Styl podróży')
            axs[0, 1].set_ylabel('Liczba użytkowników')
            axs[0, 1].tick_params(axis='x', rotation=45)

        # 3. Wykres preferowanego transportu
        transport = preferences_data['transport']
        if transport:
            # Tłumaczenie kluczy
            translated_transport = {transport_translations.get(k, k): v for k, v in transport.items()}

            axs[1, 0].bar(translated_transport.keys(), translated_transport.values(), color=sns.color_palette("crest", len(translated_transport)))
            axs[1, 0].set_title('Preferowany środek transportu')
            axs[1, 0].set_xlabel('Środek transportu')
            axs[1, 0].set_ylabel('Liczba użytkowników')
            axs[1, 0].tick_params(axis='x', rotation=45)

        # 4. Wykres preferowanego klimatu
        climate = preferences_data['climate_preference']
        if climate:
            # Tłumaczenie kluczy
            translated_climate = {climate_translations.get(k, k): v for k, v in climate.items()}

            axs[1, 1].pie(translated_climate.values(), labels=translated_climate.keys(), autopct='%1.1f%%',
                      startangle=90, colors=sns.color_palette("pastel", len(translated_climate)))
            axs[1, 1].set_title('Preferowany klimat')
            axs[1, 1].axis('equal')

        plt.tight_layout()

        # Zapisz wykres do bufora
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png')
        buffer.seek(0)
        image_png = buffer.getvalue()
        buffer.close()

        # Konwersja do base64
        graphic = base64.b64encode(image_png).decode('utf-8')

        return graphic
    except Exception as e:
        print(f"Błąd podczas generowania wykresu preferencji użytkowników: {e}")
        return None

def generate_conversion_chart():
    """
    Generuje wykres konwersji (stosunek przeglądania do rezerwacji)
    """
    try:
        conversion_data = get_conversion_data()

        if not conversion_data or not conversion_data['trips']:
            return None

        # Wyczyść poprzednie wykresy
        plt.clf()
        plt.close('all')

        # Utwórz nowy wykres
        plt.figure(figsize=(12, 10))

        # Wybierz top 10 wycieczek z najwyższym współczynnikiem konwersji
        top_trips = conversion_data['trips'][:10]

        # Przygotuj dane do wykresu
        # Używamy tylko części nazwy przed myślnikiem lub pierwszych 10 znaków
        short_names = []
        for i, trip in enumerate(top_trips):
            title = trip['trip_title']
            if '-' in title:
                short_name = title.split('-')[0].strip()
                # Dodatkowo skracamy, jeśli część przed myślnikiem jest zbyt długa
                if len(short_name) > 12:
                    short_name = short_name[:10] + '...'
            elif ' ' in title:
                # Jeśli nie ma myślnika, ale jest spacja, bierzemy tylko pierwsze słowo
                short_name = title.split(' ')[0]
                if len(short_name) > 12:
                    short_name = short_name[:10] + '...'
            else:
                short_name = title[:10] + '...' if len(title) > 10 else title
            short_names.append(f"{i+1}. {short_name}")

        conversion_rates = [trip['conversion_rate'] for trip in top_trips]
        views = [trip['views'] for trip in top_trips]
        reservations = [trip['reservations'] for trip in top_trips]

        # Utwórz wykres słupkowy dla współczynnika konwersji
        ax1 = plt.subplot(111)
        bars = ax1.bar(short_names, conversion_rates, color='skyblue')
        ax1.set_xlabel('Wycieczka')
        ax1.set_ylabel('Współczynnik konwersji (%)', color='skyblue')
        ax1.tick_params(axis='y', labelcolor='skyblue')

        # Dodanie etykiet z wartościami
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{height:.1f}%', ha='center', va='bottom')

        # Utwórz drugi wykres dla liczby wyświetleń i rezerwacji
        ax2 = ax1.twinx()
        ax2.plot(short_names, views, 'ro-', label='Wyświetlenia')
        ax2.plot(short_names, reservations, 'go-', label='Rezerwacje')
        ax2.set_ylabel('Liczba', color='red')
        ax2.tick_params(axis='y', labelcolor='red')
        ax2.legend(loc='upper right')

        # Dodaj informację o ogólnym współczynniku konwersji
        plt.title(f'Współczynnik konwersji dla top 10 wycieczek (Ogólny: {conversion_data["overall_conversion_rate"]:.2f}%)')

        # Obróć etykiety osi X pod większym kątem (70 stopni) dla lepszej czytelności
        plt.xticks(rotation=70, ha='right')

        # Dodajemy więcej miejsca na dole wykresu dla etykiet
        plt.subplots_adjust(bottom=0.25)

        # Dostosowujemy układ z uwzględnieniem dodatkowego miejsca na dole
        plt.tight_layout(rect=[0, 0.05, 1, 0.95])

        # Zapisz wykres do bufora
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png')
        buffer.seek(0)
        image_png = buffer.getvalue()
        buffer.close()

        # Konwersja do base64
        graphic = base64.b64encode(image_png).decode('utf-8')

        return graphic
    except Exception as e:
        print(f"Błąd podczas generowania wykresu konwersji: {e}")
        return None

def get_business_analysis_data():
    """
    Pobiera wszystkie dane potrzebne do analizy biznesowej
    """
    try:
        # Pobierz dane sprzedażowe
        monthly_sales = get_monthly_sales_data()
        yearly_sales = get_yearly_sales_data()

        # Pobierz statystyki rezerwacji
        reservation_stats = get_reservation_stats()

        # Pobierz najpopularniejsze kierunki
        top_destinations = get_top_destinations()

        # Pobierz najlepiej oceniane wycieczki
        top_rated_trips = get_top_rated_trips()

        # Pobierz dane o obsłudze klienta
        customer_service = get_customer_service_data()

        # Generuj wykresy
        monthly_sales_chart = generate_monthly_sales_chart()
        top_destinations_chart = generate_top_destinations_chart()
        customer_service_chart = generate_customer_service_chart()
        top_rated_trips_chart = generate_top_rated_trips_chart()

        # Pobierz dane o preferencjach użytkowników
        user_preferences_data = get_user_preferences_data()
        user_preferences_chart = generate_user_preferences_chart()

        # Pobierz dane o konwersji
        conversion_data = get_conversion_data()
        conversion_chart = generate_conversion_chart()

        # Generuj raport AI
        ai_insights = generate_ai_insights()

        return {
            'monthly_sales': monthly_sales,
            'yearly_sales': yearly_sales,
            'reservation_stats': reservation_stats,
            'top_destinations': top_destinations,
            'top_rated_trips': top_rated_trips,
            'customer_service': customer_service,
            'monthly_sales_chart': monthly_sales_chart,
            'top_destinations_chart': top_destinations_chart,
            'customer_service_chart': customer_service_chart,
            'top_rated_trips_chart': top_rated_trips_chart,
            'user_preferences': user_preferences_data,
            'user_preferences_chart': user_preferences_chart,
            'conversion_data': conversion_data,
            'conversion_chart': conversion_chart,
            'ai_insights': ai_insights
        }
    except Exception as e:
        print(f"Błąd podczas pobierania danych analizy biznesowej: {e}")
        # Zwróć podstawowe dane, aby strona mogła się załadować
        return {
            'monthly_sales': pd.DataFrame(),
            'yearly_sales': pd.DataFrame(),
            'reservation_stats': {
                'total_reservations': 0,
                'confirmed_reservations': 0,
                'cancelled_reservations': 0,
                'pending_reservations': 0,
                'avg_reservation_value': 0,
                'total_revenue': 0
            },
            'top_destinations': [],
            'top_rated_trips': [],
            'customer_service': {
                'tickets_by_status': [],
                'total_tickets': 0,
                'open_tickets': 0,
                'closed_tickets': 0,
                'today_tickets': 0,
                'current_month_tickets': 0,
                'current_year_tickets': 0
            },
            'monthly_sales_chart': None,
            'top_destinations_chart': None,
            'customer_service_chart': None,
            'top_rated_trips_chart': None,
            'user_preferences': None,
            'user_preferences_chart': None,
            'conversion_data': None,
            'conversion_chart': None,
            'ai_insights': "Brak danych do analizy. Dodaj rezerwacje i zgłoszenia do systemu, aby wygenerować raport."
        }
