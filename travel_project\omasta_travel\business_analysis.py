import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # <PERSON><PERSON><PERSON>wamy Agg jako backend, aby d<PERSON><PERSON><PERSON><PERSON> bez GUI
import seaborn as sns
import io
import base64
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from django.db.models import Count, Sum, Avg, F, Q
from django.db.models.functions import TruncMonth, TruncYear, ExtractMonth, ExtractYear
from django.utils import timezone
import google.generativeai as genai
from django.conf import settings
import json

from .models import (
    Trip, 
    Reservation, 
    ReservationPerson, 
    Ticket, 
    Review, 
    Country, 
    Destination
)

# Konfiguracja API Gemini
genai.configure(api_key=settings.GEMINI_API_KEY)

def get_gemini_model():
    return genai.GenerativeModel('gemini-2.0-flash')

def get_monthly_sales_data(months=12):
    """
    Pobiera dane o sprzedaży miesięcznej z ostatnich X miesięcy
    """
    end_date = timezone.now().date()
    start_date = end_date - relativedelta(months=months)
    
    # Pobierz rezerwacje z określonego okresu
    reservations = Reservation.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    ).annotate(
        month=TruncMonth('created_at')
    ).values('month').annotate(
        count=Count('id'),
        total_revenue=Sum('total_price')
    ).order_by('month')
    
    # Konwersja do DataFrame
    df = pd.DataFrame(list(reservations))
    
    # Jeśli nie ma danych, zwróć pusty DataFrame
    if df.empty:
        return pd.DataFrame(columns=['month', 'count', 'total_revenue'])
    
    return df

def get_yearly_sales_data(years=3):
    """
    Pobiera dane o sprzedaży rocznej z ostatnich X lat
    """
    end_date = timezone.now().date()
    start_date = end_date - relativedelta(years=years)
    
    # Pobierz rezerwacje z określonego okresu
    reservations = Reservation.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    ).annotate(
        year=TruncYear('created_at')
    ).values('year').annotate(
        count=Count('id'),
        total_revenue=Sum('total_price')
    ).order_by('year')
    
    # Konwersja do DataFrame
    df = pd.DataFrame(list(reservations))
    
    # Jeśli nie ma danych, zwróć pusty DataFrame
    if df.empty:
        return pd.DataFrame(columns=['year', 'count', 'total_revenue'])
    
    return df

def get_top_destinations(limit=10):
    """
    Pobiera najpopularniejsze kierunki podróży
    """
    top_destinations = Reservation.objects.values(
        'trip__destination__name', 
        'trip__destination__country__name'
    ).annotate(
        count=Count('id'),
        revenue=Sum('total_price')
    ).order_by('-count')[:limit]
    
    return list(top_destinations)

def get_customer_service_data():
    """
    Pobiera dane o obsłudze klienta (zgłoszenia, bilety)
    """
    # Zgłoszenia według statusu
    tickets_by_status = Ticket.objects.values('status').annotate(
        count=Count('id')
    ).order_by('status')
    
    # Zgłoszenia według kategorii
    tickets_by_category = Ticket.objects.values('category').annotate(
        count=Count('id')
    ).order_by('category')
    
    # Średni czas rozwiązania zgłoszenia (dla zamkniętych zgłoszeń)
    closed_tickets = Ticket.objects.filter(
        status='closed', 
        closed_at__isnull=False
    )
    
    avg_resolution_time = None
    if closed_tickets.exists():
        # Oblicz średni czas rozwiązania w dniach
        resolution_times = []
        for ticket in closed_tickets:
            if ticket.created_at and ticket.closed_at:
                delta = ticket.closed_at - ticket.created_at
                resolution_times.append(delta.total_seconds() / (60*60*24))  # konwersja na dni
        
        if resolution_times:
            avg_resolution_time = sum(resolution_times) / len(resolution_times)
    
    return {
        'tickets_by_status': list(tickets_by_status),
        'tickets_by_category': list(tickets_by_category),
        'avg_resolution_time': avg_resolution_time,
        'total_tickets': Ticket.objects.count(),
        'open_tickets': Ticket.objects.filter(status='open').count(),
        'closed_tickets': Ticket.objects.filter(status='closed').count()
    }

def get_reservation_stats():
    """
    Pobiera statystyki rezerwacji
    """
    total_reservations = Reservation.objects.count()
    confirmed_reservations = Reservation.objects.filter(status='confirmed').count()
    cancelled_reservations = Reservation.objects.filter(status='cancelled').count()
    pending_reservations = Reservation.objects.filter(status='pending').count()
    
    # Średnia wartość rezerwacji
    avg_reservation_value = Reservation.objects.aggregate(
        avg_value=Avg('total_price')
    )['avg_value'] or 0
    
    # Całkowity przychód
    total_revenue = Reservation.objects.filter(
        status='confirmed'
    ).aggregate(
        total=Sum('total_price')
    )['total'] or 0
    
    return {
        'total_reservations': total_reservations,
        'confirmed_reservations': confirmed_reservations,
        'cancelled_reservations': cancelled_reservations,
        'pending_reservations': pending_reservations,
        'avg_reservation_value': avg_reservation_value,
        'total_revenue': total_revenue
    }

def generate_monthly_sales_chart():
    """
    Generuje wykres sprzedaży miesięcznej
    """
    df = get_monthly_sales_data()
    
    if df.empty or 'month' not in df.columns:
        return None
    
    plt.figure(figsize=(10, 6))
    
    # Formatowanie dat na osi X
    if 'month' in df.columns:
        df['month_str'] = df['month'].dt.strftime('%b %Y')
    
    # Wykres słupkowy liczby rezerwacji
    ax1 = plt.subplot(111)
    bars = ax1.bar(df['month_str'], df['count'], color='skyblue')
    ax1.set_xlabel('Miesiąc')
    ax1.set_ylabel('Liczba rezerwacji', color='skyblue')
    ax1.tick_params(axis='y', labelcolor='skyblue')
    
    # Dodanie etykiet z wartościami
    for bar in bars:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                 f'{int(height)}', ha='center', va='bottom')
    
    # Wykres liniowy przychodu
    ax2 = ax1.twinx()
    line = ax2.plot(df['month_str'], df['total_revenue'], color='red', marker='o', linestyle='-', linewidth=2, markersize=6)
    ax2.set_ylabel('Przychód (PLN)', color='red')
    ax2.tick_params(axis='y', labelcolor='red')
    
    # Dodanie etykiet z wartościami
    for i, value in enumerate(df['total_revenue']):
        ax2.text(i, value + 50, f'{int(value)} PLN', ha='center', va='bottom', color='red')
    
    plt.title('Miesięczna sprzedaż i przychód')
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # Zapisz wykres do bufora
    buffer = io.BytesIO()
    plt.savefig(buffer, format='png')
    buffer.seek(0)
    image_png = buffer.getvalue()
    buffer.close()
    
    # Konwersja do base64
    graphic = base64.b64encode(image_png).decode('utf-8')
    
    return graphic

def generate_top_destinations_chart(limit=5):
    """
    Generuje wykres najpopularniejszych kierunków
    """
    top_destinations = get_top_destinations(limit)
    
    if not top_destinations:
        return None
    
    df = pd.DataFrame(top_destinations)
    
    plt.figure(figsize=(10, 6))
    
    # Tworzenie etykiet dla wykresu
    labels = [f"{dest['trip__destination__name']} ({dest['trip__destination__country__name']})" 
              for dest in top_destinations]
    
    # Wykres słupkowy
    bars = plt.bar(labels, df['count'], color=sns.color_palette("viridis", len(labels)))
    
    # Dodanie etykiet z wartościami
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                 f'{int(height)}', ha='center', va='bottom')
    
    plt.title('Najpopularniejsze kierunki podróży')
    plt.xlabel('Destynacja')
    plt.ylabel('Liczba rezerwacji')
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    
    # Zapisz wykres do bufora
    buffer = io.BytesIO()
    plt.savefig(buffer, format='png')
    buffer.seek(0)
    image_png = buffer.getvalue()
    buffer.close()
    
    # Konwersja do base64
    graphic = base64.b64encode(image_png).decode('utf-8')
    
    return graphic

def generate_customer_service_chart():
    """
    Generuje wykres danych obsługi klienta
    """
    service_data = get_customer_service_data()
    
    if not service_data['tickets_by_status']:
        return None
    
    plt.figure(figsize=(10, 6))
    
    # Dane do wykresu kołowego
    labels = [item['status'] for item in service_data['tickets_by_status']]
    sizes = [item['count'] for item in service_data['tickets_by_status']]
    
    # Wykres kołowy
    plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90, 
            colors=sns.color_palette("pastel", len(labels)))
    plt.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle
    plt.title('Zgłoszenia według statusu')
    plt.tight_layout()
    
    # Zapisz wykres do bufora
    buffer = io.BytesIO()
    plt.savefig(buffer, format='png')
    buffer.seek(0)
    image_png = buffer.getvalue()
    buffer.close()
    
    # Konwersja do base64
    graphic = base64.b64encode(image_png).decode('utf-8')
    
    return graphic

def generate_ai_insights():
    """
    Generuje wnioski i rekomendacje przy użyciu AI (Google Gemini)
    """
    model = get_gemini_model()
    
    # Przygotuj dane do analizy
    monthly_sales = get_monthly_sales_data()
    yearly_sales = get_yearly_sales_data()
    top_destinations = get_top_destinations()
    customer_service = get_customer_service_data()
    reservation_stats = get_reservation_stats()
    
    # Konwersja danych do formatu JSON
    analysis_data = {
        'monthly_sales': monthly_sales.to_dict('records') if not monthly_sales.empty else [],
        'yearly_sales': yearly_sales.to_dict('records') if not yearly_sales.empty else [],
        'top_destinations': top_destinations,
        'customer_service': customer_service,
        'reservation_stats': reservation_stats
    }
    
    # Prompt dla modelu AI
    prompt = f"""
    Jako ekspert ds. analizy biznesowej biura podróży, przeanalizuj poniższe dane i przygotuj krótki raport 
    zawierający kluczowe wnioski i rekomendacje. Raport powinien być napisany w języku polskim i zawierać 
    maksymalnie 5 najważniejszych wniosków oraz 3-5 konkretnych rekomendacji biznesowych.
    
    DANE DO ANALIZY:
    {json.dumps(analysis_data, indent=2, default=str)}
    
    Twój raport powinien zawierać:
    1. Krótkie podsumowanie wyników finansowych
    2. Analiza trendów sprzedażowych
    3. Analiza popularności kierunków
    4. Ocena obsługi klienta
    5. Konkretne rekomendacje biznesowe
    
    Format raportu:
    - Tytuł: "Raport Analizy Biznesowej Biura Podróży"
    - Sekcja 1: Podsumowanie wyników
    - Sekcja 2: Kluczowe wnioski (punkty)
    - Sekcja 3: Rekomendacje (punkty)
    
    Pisz zwięźle i konkretnie, skupiając się na najważniejszych informacjach.
    """
    
    try:
        response = model.generate_content(prompt)
        return response.text
    except Exception as e:
        print(f"Błąd podczas generowania raportu AI: {e}")
        return "Nie udało się wygenerować raportu AI. Spróbuj ponownie później."

def get_business_analysis_data():
    """
    Pobiera wszystkie dane potrzebne do analizy biznesowej
    """
    # Pobierz dane sprzedażowe
    monthly_sales = get_monthly_sales_data()
    yearly_sales = get_yearly_sales_data()
    
    # Pobierz statystyki rezerwacji
    reservation_stats = get_reservation_stats()
    
    # Pobierz najpopularniejsze kierunki
    top_destinations = get_top_destinations()
    
    # Pobierz dane o obsłudze klienta
    customer_service = get_customer_service_data()
    
    # Generuj wykresy
    monthly_sales_chart = generate_monthly_sales_chart()
    top_destinations_chart = generate_top_destinations_chart()
    customer_service_chart = generate_customer_service_chart()
    
    # Generuj raport AI
    ai_insights = generate_ai_insights()
    
    return {
        'monthly_sales': monthly_sales,
        'yearly_sales': yearly_sales,
        'reservation_stats': reservation_stats,
        'top_destinations': top_destinations,
        'customer_service': customer_service,
        'monthly_sales_chart': monthly_sales_chart,
        'top_destinations_chart': top_destinations_chart,
        'customer_service_chart': customer_service_chart,
        'ai_insights': ai_insights
    }
