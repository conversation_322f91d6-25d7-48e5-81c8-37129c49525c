{% extends 'base.html' %}
{% load static %}

{% block title %}Zgłoś zapytanie{% endblock %}

{% block content %}
<div class="container">
    <h2 class="mb-4"><PERSON>rz kontaktowy</h2>

    {% if user.is_authenticated %}
    <div class="card shadow-sm">
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                
                <div class="mb-3">
                    <label for="id_subject" class="form-label">Temat zapytania</label>
                    {{ form.subject }}
                </div>

                <div class="mb-3">
                    <label for="id_message" class="form-label">Treść zapytania</label>
                    {{ form.message }}
                </div>

                <div class="mb-3">
                    <button type="submit" class="btn btn-primary w-100">Wyślij zgłoszenie</button>
                </div>
            </form>
        </div>
    </div>
    {% else %}
    <p>A<PERSON> zada<PERSON> pytanie, musis<PERSON> się <a href="{% url 'login' %}">zalogować</a>.</p>
    {% endif %}
</div>
{% endblock %}
