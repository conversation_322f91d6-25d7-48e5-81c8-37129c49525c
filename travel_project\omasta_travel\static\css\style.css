/* Style navbara */
nav.navbar {
    background-color: #1a3c5e !important;
    padding: 1rem 1.5rem !important;
    box-shadow: 0 20px 100px rgba(0, 0, 0, 0.15) !important;
}

/* Logo */
nav.navbar .navbar-brand {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
    transition: color 0.3s ease; /* Płynne przejście jak w przykładzie */
}
nav.navbar .navbar-brand:hover {
    color: #a3c6e6;
}

/* Linki nawig<PERSON>jne */
nav.navbar .navbar-nav .nav-item .nav-link {
    color: #e6f0fa !important;
    padding: 0.5rem 1rem !important;
    transition: color 0.3s ease !important; /* Płynne przejście jak w przykładzie */
}
nav.navbar .navbar-nav .nav-item .nav-link:hover {
    color: #66b3ff !important;
}

/* Dropdown */
nav.navbar .navbar-nav .dropdown-menu {
    background-color: #1a3c5e;
    border: none;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    opacity: 0; /* Początkowa przezroczystość */
    transform: translateY(10px); /* Lekkie przesunięcie w dół */
    transition: opacity 0.3s ease, transform 0.3s ease; /* Płynne przejścia jak w przykładzie */
    pointer-events: none; /* Wyłączenie interakcji, gdy niewidoczne */
}
nav.navbar .navbar-nav .dropdown:hover .dropdown-menu {
    display: block;
    opacity: 1; /* Pełna widoczność */
    transform: translateY(0); /* Powrót na miejsce */
    pointer-events: auto; /* Włączenie interakcji */
}
nav.navbar .navbar-nav .dropdown-menu .dropdown-item {
    color: #e6f0fa;
    padding: 0.5rem 1rem;
    transition: background-color 0.3s ease, color 0.3s ease; /* Płynne zmiany jak w przykładzie */
}
nav.navbar .navbar-nav .dropdown-menu .dropdown-item:hover {
    background-color: #2a5a8c;
    color: #ffffff;
}

/* Usunięcie domyślnej strzałki w dropdownach */
.navbar-nav .dropdown-toggle::after {
    display: none;
}

/* Przycisk wylogowania */
nav.navbar .navbar-nav .btn-link.nav-link {
    color: #e6f0fa !important;
    transition: color 0.3s ease !important; /* Płynne przejście jak w przykładzie */
}
nav.navbar .navbar-nav .btn-link.nav-link:hover {
    color: #ff6666 !important;
    text-decoration: none !important;
}

nav.navbar .navbar-nav .nav-item .nav-link[href*="login"]:hover,
nav.navbar .navbar-nav .nav-item .nav-link[href*="register"]:hover {
    color: #66bb6a !important; /* Jaśniejszy zielony na hover */
}

/* Hamburger menu */
nav.navbar .navbar-toggler {
    border: none;
}
nav.navbar .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3E%3Cpath stroke='rgba(255, 255, 255, 0.9)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}

/* Stylizacja hamburgera (ukrytego w wersji desktop) */
.navbar .hamburger-menu {
    display: none;
    cursor: pointer;
    font-size: 24px;
}

/* Pokaż hamburger w wersji mobilnej */
@media (max-width: 991px) {
    .navbar .hamburger-menu {
        display: block;
    }
    .navbar-collapse {
        display: none;
    }
    .navbar-collapse.show {
        display: block;
    }
}

/* Paginacja */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    font-family: Arial, sans-serif;
}

.step-links {
    display: flex;
    align-items: center;
    gap: 10px; /* Odstęp między elementami */
}

.btn-outline-primary {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 20px; /* Zaokrąglone rogi */
    text-decoration: none;
    color: #007bff; /* Kolor podstawowy Bootstrap */
    border: 1px solid #007bff;
    transition: all 0.3s ease; /* Płynne przejście jak w przykładzie */
}
.btn-outline-primary:hover {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
    text-decoration: none;
}

.current {
    padding: 8px 16px;
    font-size: 14px;
    color: #333;
    background-color: #f8f9fa; /* Jasne tło */
    border-radius: 20px;
    border: 1px solid #ddd;
}

/* Styl dla aktywnych przycisków (opcjonalne) */
.step-links a:active {
    background-color: #0056b3;
    border-color: #0056b3;
    color: white;
}

/* Responsywność */
@media (max-width: 576px) {
    .step-links {
        flex-wrap: wrap; /* Zwijanie na mniejszych ekranach */
        gap: 5px;
    }
    .btn-outline-primary,
    .current {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* Style home */

/* Duży baner */
.hero-banner {
    position: relative;
    background-size: cover;
    background-position: bottom center; /* Ustawienie obrazu na dole */
    height: 450px; /* Wysokość banera */
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.5);
    border-radius: 15px; /* Zaokrąglone rogi jak w przykładzie */
    overflow: hidden; /* Jak w przykładzie */
}

.hero-banner .hero-content {
    text-align: center;
    padding: 2rem; /* Jak w przykładzie */
    width: 100%; /* Jak w przykładzie */
}

.hero-banner h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.hero-banner p {
    font-size: 1.5rem;
    margin-bottom: 20px;
}

.hero-banner .btn {
    font-size: 1.25rem;
    padding: 10px 30px;
    background-color: #ff9900;
    border-color: #ff9900;
    border-radius: 5px; /* Jak w przykładzie */
    transition: all 0.3s ease; /* Płynne przejście jak w przykładzie */
}
.hero-banner .btn:hover {
    background-color: #cc7a00;
    border-color: #cc7a00;
    transform: scale(1.05); /* Efekt jak w przykładzie */
}

/* Karty wycieczek */
.card {
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease; /* Płynne przejście jak w przykładzie */
    border-radius: 10px; /* Jak w przykładzie */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Początkowy cień jak w przykładzie */
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); /* Efekt hover jak w przykładzie */
}

.card-body {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    min-height: 200px;
    padding: 1.25rem;
}

.card-body-extra-text {
    margin-top: auto; /* Wyrównuje te elementy do dołu */
}

.row .card {
    height: 100%;
}

.trip-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 8px;
}

.card-body h5 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.card-body .card-text {
    color: #555;
}

.card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    padding: 10px;
}

.card-footer .btn {
    font-size: 0.9rem;
    padding: 8px 20px;
    border-radius: 5px; /* Jak w przykładzie */
    background-color: #007bff;
    border-color: #007bff;
    transition: all 0.3s ease; /* Płynne przejście jak w przykładzie */
}
.card-footer .btn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: scale(1.05); /* Efekt jak w przykładzie */
}

/* Animacja na przyciskach */
.btn-primary,
.btn-light {
    transition: all 0.3s ease; /* Płynne przejście jak w przykładzie */
}
.btn-primary:hover,
.btn-light:hover {
    transform: scale(1.05); /* Efekt jak w przykładzie */
}

.leady {
    display: inline-block; /* Jak w przykładzie */
    background-color: rgba(0, 0, 0, 0.5); /* Jak w przykładzie */
    padding: 10px 20px; /* Jak w przykładzie */
    border-radius: 30px; /* Jak w przykładzie */
    margin-top: 1rem; /* Jak w przykładzie */
}

.recommended-trips {
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.15)); /* Jak w przykładzie */
    padding: 40px 0; /* Jak w przykładzie */
    border-radius: 15px; /* Jak w przykładzie */
    margin: 30px 0; /* Jak w przykładzie */
}

.recommended-trips .container {
    max-width: 1140px; /* Szerokość kontenera */
    margin: 0 auto; /* Wyśrodkowanie kontenera */
}

.recommended-trips h2 {
    font-size: 2rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #333;
    position: relative;
    display: block; /* Upewnijmy się, że jest blokiem */
    padding-bottom: 10px;
    margin: 0 auto; /* Dodatkowe wyśrodkowanie */
    text-align: center; /* Wymuszenie centrowania */
    width: fit-content; /* Ustalamy szerokość na podstawie treści */
}

.recommended-trips h2::after {
    content: "";
    display: block;
    width: 80px;
    height: 4px;
    background: #007bff;
    margin: 10px auto 0;
    border-radius: 2px;
}

.flag-image {
    width: 200px; /* Stała szerokość dla wszystkich flag */
    height: 133px; /* Stała wysokość (proporcje 3:2) */
    object-fit: contain; /* Zachowuje proporcje obrazu, wypełniając przestrzeń, ale nie obcina */
    margin-top: 30px; /* Odstęp od góry */
    border-radius: 4px; /* Lekkie zaokrąglenie dla estetyki */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Delikatny cień dla głębi */
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Jak w przykładzie */
    gap: 1.5rem; /* Jak w przykładzie */
    margin-bottom: 2rem; /* Jak w przykładzie */
}

.detail-item {
    background-color: #f8f9fa; /* Jak w przykładzie */
    padding: 1.25rem; /* Jak w przykładzie */
    border-radius: 10px; /* Jak w przykładzie */
    text-align: center; /* Jak w przykładzie */
    transition: transform 0.3s ease; /* Płynne przejście jak w przykładzie */
}
.detail-item:hover {
    transform: translateY(-3px); /* Efekt jak w przykładzie */
}

.detail-item i {
    font-size: 1.5rem; /* Jak w przykładzie */
    margin-bottom: 0.5rem; /* Jak w przykładzie */
    display: block; /* Jak w przykładzie */
}

.detail-item strong {
    display: block; /* Jak w przykładzie */
    margin-bottom: 0.5rem; /* Jak w przykładzie */
    color: #495057; /* Jak w przykładzie */
}

.price-badge {
    background: linear-gradient(45deg, #f8f9fa 0%, #e9ecef 100%); /* Jak w przykładzie */
    padding: 1.5rem; /* Jak w przykładzie */
    border-radius: 10px; /* Jak w przykładzie */
    text-align: center; /* Jak w przykładzie */
}

.user-info {
    display: flex; /* Jak w przykładzie */
    align-items: center; /* Jak w przykładzie */
}

.user-circle {
    width: 40px; /* Jak w przykładzie */
    height: 40px; /* Jak w przykładzie */
    background-color: #007bff; /* Jak w przykładzie */
    color: white; /* Jak w przykładzie */
    border-radius: 50%; /* Jak w przykładzie */
    display: flex; /* Jak w przykładzie */
    align-items: center; /* Jak w przykładzie */
    justify-content: center; /* Jak w przykładzie */
    font-weight: bold; /* Jak w przykładzie */
}

/* Media queries dla responsywności */
@media (max-width: 768px) {
    .hero-banner {
        height: 300px; /* Jak w przykładzie */
    }
    .details-grid {
        grid-template-columns: 1fr; /* Jak w przykładzie */
    }
    .price-badge {
        margin-top: 2rem; /* Jak w przykładzie */
    }
}

.trip-main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.trip-image-container {
    padding: 10px;
}

.trip-info .detail-item {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
}

@media (max-width: 991px) {
    .trip-main-image {
        height: 300px;
        margin-top: 2rem;
    }
}

.message.error {
    color: red;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 10px;
    margin: 10px 0;
    border-radius: 5px;
}

/* Style for login form */
.login-form .form-group {
    margin-bottom: 1rem; /* Add space between form fields */
}

.table-custom {
    background-color: #1a3c5e; /* Zgodny z kolorystyką navbara */
    color: #ffffff; /* Białe litery dla kontrastu */
    border-radius: 12px; /* Zaokrąglone rogi jak w innych komponentach */
    overflow: hidden;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1); /* Delikatny cień jak w innych elementach */
}

.table-rounded {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1); /* Delikatny cień */
    background-color: #f8f9fa; /* Jasne tło */
}

/* Nagłówki tabeli */
.table th {
    color: white; /* Białe litery */
    text-transform: uppercase; /* Duże litery */
    letter-spacing: 1px; /* Rozstrzelenie tekstu */
    padding: 12px;
    text-align: center; /* Wyrównanie tekstu */
    font-weight: 600; /* Zwiększenie grubości czcionki dla nagłówków */
    transition: background-color 0.3s ease-in-out; /* Płynne przejście */
}

/* Wiersze tabeli */
.table tbody tr:nth-child(odd) {
    background-color: #e3eaf3; /* Jasny kolor co drugiego wiersza */
}

.table tbody tr:hover {
    background-color: #d1e0f0; /* Kolor po najechaniu */
    transition: background-color 0.3s ease-in-out;
}

/* Komórki tabeli */
.table td {
    padding: 12px;
    text-align: center; /* Wyrównanie tekstu */
    font-size: 1rem; /* Dopasowanie rozmiaru czcionki */
}

/* Obramowanie tabeli */
.table-bordered {
    border: 2px solid #1a3c5e; /* Zgodne z kolorystyką */
}

/* Kontener tabeli */
.table-container {
    margin: 20px 0;
    border-radius: 12px; /* Zaokrąglone rogi */
    overflow: hidden;
}
