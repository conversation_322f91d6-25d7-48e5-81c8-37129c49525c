<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <style>
        /* Definiowanie rozmiaru strony A4 */
        @page {
            size: A4;
            margin: 10mm;  /* Mniejszy margines */
        }

        /* Ogólny wygląd strony */
        body {
            font-family: 'DejaVuSans', sans-serif;
            line-height: 1.2;  /* Zmniejszenie odstępów między liniami */
            margin: 0;
            padding: 0;
            background-color: #f7f7f7;
            font-size: 7pt;  /* Zmniejszenie czcionki */
        }

        h1 {
            text-align: center;
            color: #333;
            font-size: 18pt;  /* Zmniejszenie rozmiaru tytułu */
            margin-bottom: 10px;  /* Zmniejszenie odstępu pod tytułem */
        }

        hr {
            border: 0;
            border-top: 1px solid #ccc;
            margin: 10px 0;  /* Zmniejszenie odstępu wokół linii */
        }

        /* Bloki treści */
        .content-block {
            background-color: #fff;
            padding: 10px;  /* Zmniejszenie wypełnienia */
            margin-bottom: 10px;  /* Zmniejszenie odstępu między blokami */
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-weight: bold;
            font-size: 12pt;  /* Zmniejszenie rozmiaru tytułu sekcji */
            color: #007bff;
            margin-bottom: 5px;  /* Zmniejszenie odstępu pod tytułem sekcji */
        }

        .content-block p {
            margin: 5px 0;  /* Zmniejszenie marginesów w paragrafach */
            font-size: 10pt;  /* Zmniejszenie rozmiaru czcionki w paragrafach */
        }

        ol {
            padding-left: 15px;
            margin: 5px 0;  /* Zmniejszenie odstępu wokół listy */
        }

        .content-block li {
            font-size: 10pt;  /* Zmniejszenie czcionki w liście */
            color: #555;
        }

        .content-block li span {
            color: #007bff;
            font-weight: bold;
        }

        /* Stopka */
        .footer {
            text-align: center;
            font-size: 9pt;  /* Zmniejszenie rozmiaru czcionki stopki */
            margin-top: 20px;  /* Zmniejszenie odstępu nad stopką */
            color: #888;
        }

        /* Stylizacja tabel */
        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th, .table td {
            padding: 6px;  /* Zmniejszenie odstępów w tabeli */
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background-color: #007bff;
            color: white;
        }

        /* Responsywność dla małych ekranów */
        @media print {
            body {
                font-size: 9pt;  /* Zmniejszenie czcionki podczas drukowania */
            }

            .footer {
                font-size: 8pt;
            }

            h1 {
                font-size: 16pt;
            }

            .content-block {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <h1>Potwierdzenie rezerwacji #{{ reservation.reservation_code }}</h1>
    <hr>

    <div class="content-block">
        <div class="section-title">Dane zamawiającego:</div>
        <p>Imię i nazwisko: {{ reservation.user.first_name }} {{ reservation.user.last_name }}</p>
        <p>Email: {{ reservation.user.email }}</p>
        <p>Data rezerwacji: {{ reservation.created_at|date:"d.m.Y" }}</p>
    </div>

    <div class="content-block">
        <div class="section-title">Informacje o wycieczce:</div>
        <p><strong>Nazwa:</strong> {{ reservation.trip.title }}</p>
        <p><strong>Destynacja:</strong> {{ reservation.trip.destination }}</p>
        <p><strong>Termin:</strong> {{ reservation.trip.start_date|date:"d.m.Y" }} - {{ reservation.trip.end_date|date:"d.m.Y" }}</p>
        <p><strong>Transport:</strong> {{ reservation.trip.get_transport_display }}</p>
        <p><strong>Typ:</strong> {{ reservation.trip.get_trip_type_display }}</p>
        <p><strong>Wyżywienie:</strong> {{ reservation.trip.get_meal_plan_display }}</p>
    </div>

    <div class="content-block">
        <div class="section-title">Szczegóły rezerwacji:</div>
        <p><strong>Uczestników:</strong> {{ reservation.number_of_people }}</p>
        <p><strong>Cena:</strong> {{ reservation.total_price }} PLN</p>
        <p><strong>Status:</strong> {{ reservation.get_status_display }}</p>
        {% if reservation.notes %}
            <p><strong>Uwagi:</strong> {{ reservation.notes }}</p>
        {% endif %}
    </div>

    <div class="content-block">
        <div class="section-title">Lista uczestników:</div>
        <ol>
            {% for person in reservation.people.all %}
                <li>{{ person.first_name }} {{ person.last_name }} 
                    (<span>
                        {% if person.is_adult %}
                            Dorosły
                        {% else %}
                            Dziecko
                        {% endif %}
                    </span>) - Pesel:{{person.pesel}}
                </li>
            {% endfor %}
        </ol>
    </div>

    <hr>
    <div class="footer">
        <p>Wygenerowano: {{ now }}</p>
        <p>Dziękujemy za wybranie naszego biura podróży!</p>
    </div>
</body>
</html>
