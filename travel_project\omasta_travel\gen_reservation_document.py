from io import BytesIO
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.core.files.base import ContentFile
from django.template.loader import render_to_string
from django.utils.timezone import now
from django.conf import settings
from .models import Reservation
from xhtml2pdf import pisa
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from xhtml2pdf.default import DEFAULT_FONT
import os
import traceback

# Ścieżka do czcionki
FONT_NAME = "DejaVuSans"
FONT_PATH = os.path.join(settings.BASE_DIR, 'omasta_travel', 'static', 'fonts', 'dejavusans.ttf')

# Rejestracja czcionki
if os.path.isfile(FONT_PATH):
    try:
        pdfmetrics.registerFont(TTFont(FONT_NAME, FONT_PATH))
        DEFAULT_FONT['helvetica'] = FONT_NAME  # <- wymuszenie zamiany domyślnej czcionki
        print("Czcionka zarejestrowana.")
    except Exception as e:
        print(f"Nie udało się zarejestrować czcionki: {e}")
else:
    print(f"Plik czcionki nie istnieje: {FONT_PATH}")

def generate_reservation_document(reservation):
    try:
        if reservation.status != 'confirmed':
            return False

        # Zabezpieczenie przed ponownym generowaniem PDF, jeśli już istnieje
        if reservation.reservation_document and reservation.reservation_document.name:
            print("Dokument już istnieje — pomijanie generowania.")
            return False

        html_string = render_to_string("confirmation_template.html", {
            "reservation": reservation,
            "now": now().strftime('%d.%m.%Y %H:%M'),
        })

        pdf_file = BytesIO()

        pisa_status = pisa.CreatePDF(
            src=html_string,
            dest=pdf_file,
            encoding='utf-8',
            show_error_as_pdf=True
        )

        if pisa_status.err:
            print(f"Błąd podczas konwersji HTML do PDF: {pisa_status.err}")
            return False

        pdf_file.seek(0)
        filename = f"reservation_confirmation_{reservation.reservation_code}.pdf"
        reservation.reservation_document.save(filename, ContentFile(pdf_file.read()), save=False)
        Reservation.objects.filter(pk=reservation.pk).update(reservation_document=reservation.reservation_document.name)
        return True

    except Exception as e:
        print(f"Błąd podczas generowania dokumentu PDF: {e}")
        traceback.print_exc()
        return False

@receiver(post_save, sender=Reservation)
def auto_generate_document(sender, instance, created, **kwargs):
    if not created:
        previous = sender.objects.filter(pk=instance.pk).first()
        
        # Tworzenie dokumentu PDF jeśli status został zmieniony na 'confirmed'
        if previous and previous.status != 'confirmed' and instance.status == 'confirmed':
            generate_reservation_document(instance)
        
        # Usuwanie dokumentu PDF jeśli status zmieniono na 'cancelled' lub 'pending'
        if instance.status in ['cancelled', 'pending'] and previous.status == 'confirmed':
            if instance.reservation_document:
                instance.reservation_document.delete(save=True)
