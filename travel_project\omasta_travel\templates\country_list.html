{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="mb-4">Wszystkie dostępne kraje</h2>

    <div class="row">
        <!-- Lewa kolumna: Filtry -->
        <aside class="col-md-3">
            <div class="filter-box p-3 bg-light rounded shadow-sm">
                <h5 class="mb-3">Filtruj kraje</h5>
                <form method="get">
                    <div class="mb-2">
                        {{ form.name.label_tag }}
                        {{ form.name }}
                    </div>
                    <div class="mb-2">
                        {{ form.continent.label_tag }}
                        {{ form.continent }}
                    </div>
                    <button type="submit" class="btn btn-primary w-100">Szukaj</button>
                    <a href="{% url 'country_list' %}" class="btn btn-secondary w-100 mt-2">Resetuj</a>
                </form>
            </div>
        </aside>

        <!-- Prawa kolumna: Lista krajów -->
        <section class="col-md-9">
            <div class="row row-cols-1 row-cols-md-3 g-4">
                {% for country in countries %}
                <div class="col">
                    <a href="{% url 'country_destinations' country.id %}" class="text-decoration-none" style="color: inherit;">
                        <div class="card h-100 text-center shadow-sm">
                            {% if country.image %}
                                <img src="{{ country.image.url }}" class="card-img-top flag-image mx-auto d-block" alt="{{ country.name }}">
                            {% endif %}
                            <div class="card-body">
                                <h5 class="card-title">{{ country.name }}</h5>
                                <p class="card-text">{{ country.description }}</p>
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">Kontynent: {{ country.get_continent_display }}</small>
                            </div>
                        </div>
                    </a>
                </div>
                {% empty %}
                <div class="col-12 d-flex justify-content-center align-items-center" style="min-height: 300px; width: 100%;">
                    <p class="text-center fs-4">Brak krajów spełniających kryteria.</p>
                </div>
                {% endfor %}
            </div>

            <!-- Paginacja -->
            <div class="pagination justify-content-center mt-4">
                <span class="step-links">
                    {% if countries.has_previous %}
                        <a href="?{% if request.GET.name %}name={{ request.GET.name }}&{% endif %}{% if request.GET.continent %}continent={{ request.GET.continent }}&{% endif %}page=1" class="btn btn-outline-primary">« pierwsza</a>
                        <a href="?{% if request.GET.name %}name={{ request.GET.name }}&{% endif %}{% if request.GET.continent %}continent={{ request.GET.continent }}&{% endif %}page={{ countries.previous_page_number }}" class="btn btn-outline-primary">poprzednia</a>
                    {% endif %}
            
                    <span class="current mx-2">
                        Strona {{ countries.number }} z {{ countries.paginator.num_pages }}
                    </span>
            
                    {% if countries.has_next %}
                        <a href="?{% if request.GET.name %}name={{ request.GET.name }}&{% endif %}{% if request.GET.continent %}continent={{ request.GET.continent }}&{% endif %}page={{ countries.next_page_number }}" class="btn btn-outline-primary">następna</a>
                        <a href="?{% if request.GET.name %}name={{ request.GET.name }}&{% endif %}{% if request.GET.continent %}continent={{ request.GET.continent }}&{% endif %}page={{ countries.paginator.num_pages }}" class="btn btn-outline-primary">ostatnia »</a>
                    {% endif %}
                </span>
            </div>
        </section>
    </div>
</div>
{% endblock %}
