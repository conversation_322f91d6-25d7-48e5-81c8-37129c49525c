import os
import json
import google.generativeai as genai
from django.conf import settings
from .models import Trip, Country, Destination, Reservation, ChatMessage

def configure_gemini():
    api_key = settings.GEMINI_API_KEY
    genai.configure(api_key=api_key)
    
    # Użyj dostrojonego modelu zamiast gemini-2.0-flash
    return genai.GenerativeModel(
        model_name="gemini-2.0-flash",
        #model_name="tunedModels/naj-ydx5flu6x0y6",
    )

# System instrukcji dla chatbota
SYSTEM_INSTRUCTION = """
Jesteś asystentem biura podróży Omasta Travel. Twoim zadaniem jest udzielanie informacji na temat oferty wycieczek, 
destynacji, krajów oraz odpowiadanie na ogólne pytania związane z podróżami.

Kluczowe informacje:
1. Nazwa biura: Omasta Travel
2. Specjalizacja: wyciecz<PERSON> zagraniczne, wcz<PERSON><PERSON>, city breaki, wycieczki objazdowe
3. Obsługiwane destynacje: różne kraje z Europy, <PERSON><PERSON><PERSON>, Afryki i innych kontynentów

Gdy rozpoznasz, że pytanie dotyczy:
- Oferty biura - użyj danych z bazy danych, które zostały przekazane w kontekście
- Ogólnej wiedzy podróżniczej - odpowiedz na podstawie swojej wiedzy
- Rezerwacji klienta - sprawdź dane z bazy tylko dla zalogowanych użytkowników

Gdy odpowiadasz zalogowanemu klientowi i pytanie dotyczy jego rezerwacji, udziel szczegółowych informacji 
na temat statusu rezerwacji, nadchodzących wycieczek itp. Dla niezalogowanych użytkowników, 
zachęć do założenia konta lub zalogowania się, aby uzyskać pełen dostęp do funkcjonalności.

Odpowiadaj uprzejmie, profesjonalnie i zawsze w języku polskim.
"""

# Przykłady few-shot prompting
FEW_SHOT_EXAMPLES = [
    {
        "user": "Jakie macie oferty wycieczek do Włoch?",
        "assistant": "Dzień dobry! W Omasta Travel oferujemy kilka atrakcyjnych wycieczek do Włoch. Na podstawie naszej aktualnej oferty, mogę polecić:\n\n1. \"Włoskie Wakacje\" - 7-dniowy pobyt w Rzymie, cena od 3200 zł za osobę, wylot 15-06-2025, transport samolotem, wyżywienie HB (śniadanie i obiadokolacja).\n\n2. \"Toskańskie krajobrazy\" - 10-dniowa wycieczka objazdowa po Toskanii, cena od 4500 zł, wylot 20-07-2025.\n\nZainteresowała Panią/Pana któraś z tych ofert? Chętnie podam więcej szczegółów lub pomogę znaleźć inne kierunki."
    },
    {
        "user": "Ile kosztuje wycieczka do Grecji dla rodziny 2+2?",
        "assistant": "Dzień dobry! W naszej ofercie Omasta Travel mamy kilka opcji wycieczek do Grecji idealnych dla rodzin. Na przykład, wycieczka \"Grecka Przygoda\" na Kretę to koszt około 2800 zł za osobę dorosłą, a dla dzieci do lat 12 oferujemy zniżkę 20%. Dla rodziny 2+2 całkowity koszt wynosiłby około 8960 zł (2800 zł × 2 + 2240 zł × 2), w zależności od wieku dzieci. Cena obejmuje zakwaterowanie w hotelu 4* z all inclusive, przelot oraz transfery. Czy chciałaby Pani/Pan poznać więcej szczegółów na temat tej lub innych greckich destynacji?"
    },
    {
        "user": "Jaki jest status mojej rezerwacji?",
        "assistant": "Aby sprawdzić status Pani/Pana rezerwacji, konieczne jest zalogowanie się na konto w systemie Omasta Travel. Po zalogowaniu będę mógł udzielić szczegółowych informacji na temat statusu rezerwacji, nadchodzących wycieczek i płatności. Jeśli jest Pani/Pan już zalogowana/y, a nie widzę tej informacji, proszę o ponowne zalogowanie lub kontakt z naszym biurem obsługi klienta pod numerem +48 123 456 789."
    },
    {
        "user": "Co powinienem zabrać na wycieczkę do Egiptu?",
        "assistant": "Planując wycieczkę do Egiptu z Omasta Travel, warto zabrać ze sobą:\n\n1. Dokumenty: paszport ważny min. 6 miesięcy od daty powrotu, kopie dokumentów, ubezpieczenie podróżne\n2. Odzież: lekkie, przewiewne ubrania, nakrycie głowy, okulary przeciwsłoneczne\n3. Kosmetyki: krem z wysokim filtrem UV, środki na komary i owady\n4. Leki: podstawowa apteczka, leki na dolegliwości żołądkowe\n5. Elektronika: adapter do gniazdek, powerbank\n6. Gotówka: część w dolarach amerykańskich (akceptowane w wielu miejscach)\n\nPamiętaj, że w Egipcie panuje klimat pustynny, więc temperatury mogą być bardzo wysokie. W naszych hotelach partnerskich dostępne są ręczniki, ale warto zabrać własny ręcznik plażowy. Czy potrzebuje Pani/Pan dodatkowych informacji na temat Egiptu?"
    },
    {
        "user": "Mam zarezerwowaną wycieczkę do Turcji, ale chcę zmienić termin",
        "assistant": "Widzę, że jest Pani/Pan zalogowana/y w systemie Omasta Travel. Na podstawie Pani/Pana rezerwacji, mamy zapisaną wycieczkę \"Turecka Riwiera\" z wylotem 12-08-2025. W celu zmiany terminu, mamy następujące możliwości:\n\n1. Możemy przenieść rezerwację na inny dostępny termin (np. 26-08-2025 lub 09-09-2025) za opłatą manipulacyjną 200 zł od osoby.\n2. Alternatywnie, można anulować obecną rezerwację (zgodnie z warunkami zwrot 70% wpłaconej kwoty) i dokonać nowej rezerwacji.\n\nW celu dokonania zmiany proszę skontaktować się z naszym konsultantem pod numerem +48 123 456 789 lub odwiedzić nasze biuro. Czy mogę pomóc w czymś jeszcze?"
    }
]

# Funkcja do klasyfikacji zapytania - bez zmian
def classify_query(query):
    """Klasyfikuje zapytanie użytkownika"""
    # Słowa kluczowe związane z ofertą wycieczek
    offer_keywords = [
        'wycieczka', 'wycieczki', 'oferta', 'destynacja', 'kraj', 'wczasy', 
        'all inclusive', 'last minute', 'promocja', 'cena', 'koszt', 'ile kosztuje',
        'bilet', 'hotel', 'rezerwacja', 'zarezerwować', 'city break', 'objazd'
    ]
    
    # Słowa kluczowe związane z rezerwacjami
    reservation_keywords = [
        'moja rezerwacja', 'moje wycieczki', 'status', 'potwierdzona', 
        'anulować', 'zmienić', 'opłacić', 'płatność', 'zarezerwowałem'
    ]
    
    query_lower = query.lower()
    
    # Sprawdzanie czy zapytanie dotyczy oferty
    if any(keyword in query_lower for keyword in offer_keywords):
        return "offer"
    
    # Sprawdzanie czy zapytanie dotyczy rezerwacji
    if any(keyword in query_lower for keyword in reservation_keywords):
        return "reservation"
    
    # Domyślnie - ogólna wiedza podróżnicza
    return "general"

# Funkcja do pobrania danych kontekstowych z bazy - bez zmian
def get_context_data(query_type, user=None):
    """Pobiera odpowiednie dane z bazy w zależności od rodzaju zapytania"""
    context_data = {}
    
    if query_type == "offer":
        # Pobierz informacje o aktualnych wycieczkach
        trips = Trip.objects.filter(is_active=True)
        countries = Country.objects.all()
        destinations = Destination.objects.all()
        
        context_data["trips"] = [
            {
                "title": trip.title,
                "description": trip.description,
                "destination": trip.destination.name,
                "country": trip.destination.country.name,
                "start_date": trip.start_date.strftime("%d-%m-%Y"),
                "end_date": trip.end_date.strftime("%d-%m-%Y"),
                "price": float(trip.price),
                "available_places": trip.available_places,
                "transport": dict(Trip.TRANSPORT_CHOICES)[trip.transport],
                "trip_type": dict(Trip.TRIP_TYPE_CHOICES)[trip.trip_type],
                "meal_plan": dict(Trip.MEAL_PLAN_CHOICES)[trip.meal_plan]
            }
            for trip in trips
        ]
        
        context_data["countries"] = [
            {
                "name": country.name,
                "continent": dict(Country.CONTINENT_CHOICES).get(country.continent, ""),
                "description": country.description
            }
            for country in countries
        ]
        
        context_data["destinations"] = [
            {
                "name": dest.name,
                "country": dest.country.name,
                "description": dest.description
            }
            for dest in destinations
        ]
    
    if query_type == "reservation" and user and user.is_authenticated:
        # Pobierz informacje o rezerwacjach użytkownika
        reservations = Reservation.objects.filter(user=user)
        
        context_data["reservations"] = [
            {
                "trip_title": res.trip.title,
                "destination": res.trip.destination.name,
                "country": res.trip.destination.country.name,
                "start_date": res.trip.start_date.strftime("%d-%m-%Y"),
                "end_date": res.trip.end_date.strftime("%d-%m-%Y"),
                "number_of_people": res.number_of_people,
                "total_price": float(res.total_price),
                "status": dict(Reservation.STATUS_CHOICES)[res.status],
                "created_at": res.created_at.strftime("%d-%m-%Y"),
                "notes": res.notes
            }
            for res in reservations
        ]
    
    return context_data

# Główna funkcja chatbota - zmodyfikowana, aby używać few-shot prompting
def get_chatbot_response(user, query):
    """Generuje odpowiedź na zapytanie użytkownika używając dostrojonego modelu z few-shot prompting"""
    model = configure_gemini()
    
    # Klasyfikuj zapytanie
    query_type = classify_query(query)
    
    # Pobierz dane kontekstowe
    context_data = get_context_data(query_type, user)
    
    # Przygotuj kontekst dla modelu z few-shot examples
    system_context = SYSTEM_INSTRUCTION + "\n\n"
    
    # Dodaj informacje o użytkowniku
    if user and user.is_authenticated:
        system_context += f"Użytkownik jest zalogowany jako: {user.username}.\n"
    else:
        system_context += "Użytkownik nie jest zalogowany.\n"
    
    # Dodaj kontekst z bazy danych
    if context_data:
        system_context += f"Kontekst z bazy danych (użyj tych informacji w odpowiedzi):\n{json.dumps(context_data, ensure_ascii=False)}\n\n"
    
    # Dodaj przykłady few-shot
    system_context += "Oto przykłady pytań i odpowiedzi, które pokazują, w jaki sposób powinieneś odpowiadać:\n\n"
    
    for i, example in enumerate(FEW_SHOT_EXAMPLES):
        system_context += f"Przykład {i+1}:\n"
        system_context += f"Pytanie użytkownika: {example['user']}\n"
        system_context += f"Twoja odpowiedź: {example['assistant']}\n\n"
    
    # Utwórz sesję chatu z historią
    chat_history = []
    if user and user.is_authenticated:
        # Opcjonalnie: Załaduj poprzednie wiadomości jako historię
        previous_messages = get_chat_history(user, limit=5)
        for msg in reversed(previous_messages):
            chat_history.append({"role": "user", "parts": [msg.message]})
            chat_history.append({"role": "model", "parts": [msg.response]})
    
    # Inicjalizacja sesji czatu
    try:
        chat_session = model.start_chat(
            history=chat_history
        )
        
        # Wyślij zapytanie wraz z systemowym kontekstem
        full_query = f"{system_context}\nZapytanie użytkownika: {query}"
        response = chat_session.send_message(full_query)
        answer = response.text
    except Exception as e:
        answer = f"Przepraszam, wystąpił problem z komunikacją z systemem AI. Proszę spróbować ponownie lub skontaktować się z obsługą biura Omasta Travel. Błąd: {str(e)}"
    
    # Zapisz interakcję do bazy danych
    if user and user.is_authenticated:
        ChatMessage.objects.create(
            user=user,
            message=query,
            response=answer
        )
    
    return answer

# Funkcje do obsługi historii czatu - bez zmian
def clear_chat_history(user):
    """Usuwa historię czatu dla danego użytkownika"""
    if user and user.is_authenticated:
        ChatMessage.objects.filter(user=user).delete()
        return True
    return False

def get_chat_history(user, limit=10):
    """Pobiera historię czatu dla danego użytkownika"""
    if user and user.is_authenticated:
        return ChatMessage.objects.filter(user=user).order_by('-created_at')[:limit]
    return []