# Generated by Django 5.1.4 on 2025-03-09 18:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('omasta_travel', '0023_alter_reservationperson_pesel'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('interests', models.JSONField(default=list, help_text='Lista zainteresowań podróżniczych')),
                ('travel_style', models.JSONField(default=list, help_text='Preferowany styl podróży')),
                ('preferred_continents', models.JSONField(default=list, help_text='Preferowane kontynenty')),
                ('budget_min', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('budget_max', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('climate_preference', models.CharField(choices=[('warm', 'Ciepły klimat'), ('cold', 'Chłodny klimat'), ('moderate', 'Umiarkowany klimat'), ('any', 'Dowolny')], default='any', max_length=20)),
                ('preferred_meal_plans', models.JSONField(default=list, help_text='Preferowane plany żywieniowe')),
                ('preferred_transport', models.JSONField(default=list, help_text='Preferowane środki transportu')),
                ('travel_duration_min', models.IntegerField(blank=True, help_text='Minimalna długość podróży (dni)', null=True)),
                ('travel_duration_max', models.IntegerField(blank=True, help_text='Maksymalna długość podróży (dni)', null=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserBrowsingHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('view_count', models.IntegerField(default=1)),
                ('last_viewed', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('trip', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='omasta_travel.trip')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='browsing_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-last_viewed'],
                'unique_together': {('user', 'trip')},
            },
        ),
    ]
