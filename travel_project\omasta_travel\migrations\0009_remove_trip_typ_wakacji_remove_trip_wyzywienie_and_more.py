# Generated by Django 5.1.4 on 2025-01-22 21:36

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('omasta_travel', '0008_remove_trip_created_at_remove_trip_image_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='trip',
            name='typ_wakacji',
        ),
        migrations.RemoveField(
            model_name='trip',
            name='wyzywienie',
        ),
        migrations.AddField(
            model_name='reservation',
            name='number_of_children',
            field=models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0)]),
        ),
        migrations.AddField(
            model_name='reservation',
            name='number_of_rooms',
            field=models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)]),
        ),
        migrations.AddField(
            model_name='trip',
            name='max_people_per_room',
            field=models.PositiveIntegerField(default=4),
        ),
        migrations.AddField(
            model_name='trip',
            name='meal_plan',
            field=models.CharField(choices=[('none', 'Brak'), ('all_inclusive', 'All Inclusive'), ('breakfast', 'Śniadanie'), ('half_board', 'Śniadanie + Obiadokolacja'), ('full_board', 'Pełne wyżywienie')], default='none', max_length=20, verbose_name='Meal plan'),
        ),
        migrations.AddField(
            model_name='trip',
            name='number_of_rooms',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AddField(
            model_name='trip',
            name='trip_type',
            field=models.CharField(choices=[('vacation', 'Wczasy'), ('city_break', 'City Break'), ('tour', 'Wycieczki objazdowe')], default='vacation', max_length=20, verbose_name='Trip type'),
        ),
    ]
