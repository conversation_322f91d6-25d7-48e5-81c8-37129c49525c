from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from .models import Country, Destination, Reservation, ReservationPerson, Review, Trip, Ticket, Message, UserPreference, UserBrowsingHistory
from captcha.fields import CaptchaField


class UserRegistrationForm(UserCreationForm):
    email = forms.EmailField(required=True)
    first_name = forms.CharField(max_length=30, required=True, label="Imię")
    last_name = forms.CharField(max_length=30, required=True, label="Nazwisko")

    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'password1', 'password2')

class ReservationForm(forms.ModelForm):
    class Meta:
        model = Reservation
        fields = ['number_of_people', 'notes']
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 4}),
        }

    def __init__(self, *args, **kwargs):
        self.trip = kwargs.pop('trip', None)
        super().__init__(*args, **kwargs)

    def clean_number_of_people(self):
        number_of_people = self.cleaned_data['number_of_people']
        if self.trip and number_of_people > self.trip.available_places:
            raise forms.ValidationError('Brak wystarczającej liczby miejsc.')
        return number_of_people

class NumberOfPeopleForm(forms.Form):
    number_of_people = forms.IntegerField(
        label="Liczba osób",
        widget=forms.TextInput(attrs={
            'class': 'form-control custom-form-control',
            'placeholder': 'Wpisz liczbę osób...'}),
        validators=[MinValueValidator(1)],
        min_value=1
    )

class ReservationPersonForm(forms.ModelForm):
    class Meta:
        model = ReservationPerson
        fields = ['first_name', 'last_name', 'pesel', 'is_adult']
        labels = {
            'first_name': 'Imię',
            'last_name': 'Nazwisko',
            'pesel': 'PESEL',
            'is_adult': 'Czy osoba jest pełnoletnia?',
        }
        widgets = {
            'is_adult': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['is_adult'].initial = True
        # Ustaw wszystkie pola jako wymagane
        self.fields['first_name'].required = True
        self.fields['last_name'].required = True
        self.fields['pesel'].required = True

class ReviewForm(forms.ModelForm):
    class Meta:
        model = Review
        fields = ['rating', 'comment']
        widgets = {
            'rating': forms.Select(choices=[(i, str(i)) for i in range(1, 6)]),
            'comment': forms.Textarea(attrs={'rows': 4}),
        }

class TripSearchForm(forms.Form):
    destination = forms.CharField(
        required=False,
        label="Destynacja",
        widget=forms.TextInput(attrs={
            'class': 'form-control custom-form-control',
            'placeholder': 'Wpisz destynację...'
        })
    )

    min_price = forms.DecimalField(
        required=False,
        label="Cena od",
        widget=forms.NumberInput(attrs={
            'class': 'form-control custom-form-control',
            'placeholder': 'Od PLN',
            'min': '0',
            'step': '100'
        })
    )

    max_price = forms.DecimalField(
        required=False,
        label="Cena do",
        widget=forms.NumberInput(attrs={
            'class': 'form-control custom-form-control',
            'placeholder': 'Do PLN',
            'min': '0',
            'step': '100'
        })
    )

    start_date = forms.DateField(
        required=False,
        label="Data wyjazdu",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-control custom-form-control custom-date-input'
        })
    )

    end_date = forms.DateField(
        required=False,
        label="Data powrotu",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-control custom-form-control custom-date-input'
        })
    )

    transport = forms.ChoiceField(
        choices=[('', 'Wszystkie rodzaje transportu')] + Trip.TRANSPORT_CHOICES,
        required=False,
        label="Transport",
        widget=forms.Select(attrs={
            'class': 'form-select custom-form-select'
        })
    )

    trip_type = forms.ChoiceField(
        choices=[('', 'Wszystkie rodzaje wycieczek')] + Trip.TRIP_TYPE_CHOICES,
        required=False,
        label="Rodzaj wycieczki",
        widget=forms.Select(attrs={
            'class': 'form-select custom-form-select'
        })
    )

    meal_plan = forms.ChoiceField(
        choices=[('', 'Wszystkie opcje wyżywienia')] + Trip.MEAL_PLAN_CHOICES,
        required=False,
        label="Wyżywienie",
        widget=forms.Select(attrs={
            'class': 'form-select custom-form-select'
        })
    )


class CountrySearchForm(forms.Form):
    name = forms.CharField(
        required=False,
        label="Państwo",
        widget=forms.TextInput(attrs={
            'placeholder': 'Wpisz nazwę kraju',
            'class': 'form-control',
        })
    )
    continent = forms.ChoiceField(
        choices=[('', 'Wybierz kontynent')] + Country.CONTINENT_CHOICES,
        required=False,
        label="Kontynent",
        widget=forms.Select(attrs={
            'class': 'form-select custom-form-select'
        })
    )

class TicketForm(forms.ModelForm):
    message = forms.CharField(
        label="Wiadomość",
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
        required=True
    )

    class Meta:
        model = Ticket
        fields = ['subject', 'message']
        labels = {
            'subject': 'Temat',
        }
        widgets = {
            'subject': forms.TextInput(attrs={'class': 'form-control'}),
        }

    def save(self, commit=True, user=None):
        ticket = super().save(commit=False)

        if user and user.is_authenticated:
            ticket.user = user

        if commit:
            ticket.save()
            Message.objects.create(
                ticket=ticket,
                user=user,
                text=self.cleaned_data['message']
            )
        return ticket

class MessageForm(forms.ModelForm):
    class Meta:
        model = Message
        fields = ['text']
        labels = {
            'text': 'Wiadomość',
        }
        widgets = {
            'text': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

class ChatForm(forms.Form):
    message = forms.CharField(
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Wpisz swoją wiadomość...'
            }
        )
    )

class UserPreferenceForm(forms.ModelForm):
    interests = forms.MultipleChoiceField(
        choices=UserPreference.INTEREST_CHOICES,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-inline'}),
        required=False,
        label='Zainteresowania',
        help_text='Wybierz swoje zainteresowania'
    )
    travel_style = forms.MultipleChoiceField(
        choices=UserPreference.TRAVEL_STYLE_CHOICES,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-inline'}),
        required=False,
        label='Styl podróży',
        help_text='Wybierz preferowany styl podróży'
    )
    preferred_continents = forms.MultipleChoiceField(
        choices=Country.CONTINENT_CHOICES,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-inline'}),
        required=False,
        label='Preferowane kontynenty',
        help_text='Wybierz kontynenty, które chcesz odwiedzić'
    )
    preferred_meal_plans = forms.MultipleChoiceField(
        choices=Trip.MEAL_PLAN_CHOICES,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-inline'}),
        required=False,
        label='Preferowane wyżywienie',
        help_text='Wybierz preferowane opcje wyżywienia'
    )
    preferred_transport = forms.MultipleChoiceField(
        choices=Trip.TRANSPORT_CHOICES,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-inline'}),
        required=False,
        label='Preferowany transport',
        help_text='Wybierz preferowane środki transportu'
    )

    class Meta:
        model = UserPreference
        fields = [
            'interests', 'travel_style', 'preferred_continents',
            'budget_min', 'budget_max', 'climate_preference',
            'preferred_meal_plans', 'preferred_transport',
            'travel_duration_min', 'travel_duration_max'
        ]
        widgets = {
            'budget_min': forms.NumberInput(attrs={
                'placeholder': 'Minimalny budżet',
                'class': 'form-control',
                'min': '0',
                'step': '100'
            }),
            'budget_max': forms.NumberInput(attrs={
                'placeholder': 'Maksymalny budżet',
                'class': 'form-control',
                'min': '0',
                'step': '100'
            }),
            'climate_preference': forms.Select(attrs={
                'class': 'form-select',
                'aria-label': 'Wybierz preferowany klimat'
            }),
            'travel_duration_min': forms.NumberInput(attrs={
                'placeholder': 'Min. dni',
                'class': 'form-control',
                'min': '1',
                'max': '365'
            }),
            'travel_duration_max': forms.NumberInput(attrs={
                'placeholder': 'Max. dni',
                'class': 'form-control',
                'min': '1',
                'max': '365'
            }),
        }
        labels = {
            'budget_min': 'Minimalny budżet',
            'budget_max': 'Maksymalny budżet',
            'climate_preference': 'Preferowany klimat',
            'travel_duration_min': 'Minimalna długość podróży (dni)',
            'travel_duration_max': 'Maksymalna długość podróży (dni)'
        }
        help_texts = {
            'budget_min': 'Podaj minimalny budżet w PLN',
            'budget_max': 'Podaj maksymalny budżet w PLN',
            'climate_preference': 'Wybierz preferowany klimat na wyjazd',
            'travel_duration_min': 'Podaj minimalną ilość dni na wyjazd',
            'travel_duration_max': 'Podaj maksymalną ilość dni na wyjazd'
        }


class CaptchaForm(forms.Form):
    captcha = CaptchaField()
