{% extends 'base.html' %}

{% block title %}{% if destination %}Wycieczki dla {{ destination.name }}{% else %}Lista wycieczek{% endif %}{% endblock %}

{% block content %}
<h2 class="mb-4">
    {% if destination %}
        Wycieczki dla destynacji: {{ destination.name }}
    {% else %}
        Wszystkie dostępne wycieczki
    {% endif %}
</h2>

<div class="container">
    <div class="row">
        <!-- Lewa kolumna: Filtry -->
        <aside class="col-md-3">
            <div class="filter-box p-3 bg-light rounded shadow-sm">
                <h5 class="mb-3">Filtruj wyniki</h5>
                <form method="get">
                    <div class="mb-2">
                        {{ form.destination.label_tag }}
                        {{ form.destination }}
                    </div>
                    <div class="mb-2">
                        {{ form.min_price.label_tag }}
                        {{ form.min_price }}
                    </div>
                    <div class="mb-2">
                        {{ form.max_price.label_tag }}
                        {{ form.max_price }}
                    </div>
                    <div class="mb-2">
                        {{ form.start_date.label_tag }}
                        {{ form.start_date }}
                    </div>
                    <div class="mb-2">
                        {{ form.end_date.label_tag }}
                        {{ form.end_date }}
                    </div>
                    <div class="mb-2">
                        {{ form.transport.label_tag }}
                        {{ form.transport }}
                    </div>
                    <div class="mb-2">
                        {{ form.trip_type.label_tag }}
                        {{ form.trip_type }}
                    </div>
                    <div class="mb-2">
                        {{ form.meal_plan.label_tag }}
                        {{ form.meal_plan }}
                    </div>
                    <button type="submit" class="btn btn-primary w-100">Szukaj</button>
                    <a href="{% url 'trip_list' %}" class="btn btn-secondary w-100 mt-2">Resetuj</a>
                </form>
            </div>
        </aside>

        <!-- Prawa kolumna: Lista wycieczek -->
<section class="col-md-9">
    <div class="row row-cols-1 row-cols-md-3 g-4">
        {% for trip in trips %}
        <div class="col">
            <div class="card h-100 shadow-sm">
                {% if trip.destination.image %}
                    <img src="{{ trip.destination.image.url }}" class="card-img-top trip-image" alt="{{ trip.title }}">
                {% endif %}
                <div class="card-body">
                    <h5 class="card-title">{{ trip.title }}</h5>
                    <p class="card-text">{{ trip.description|truncatewords:20 }}</p>
                    <div class="card-body-extra-text">
                        <p class="card-text"><small class="text-muted">{{ trip.start_date }} - {{ trip.end_date }}</small></p>
                        <p class="card-text"><strong>Cena: {{ trip.price }} PLN</strong></p>
                        <p class="card-text">
                            {% if trip.available_places > 0 %}
                                Dostępne miejsca: {{ trip.available_places }}
                            {% else %}
                                <strong>Brak dostępnych miejsc</strong>
                            {% endif %}
                        </p>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'trip_detail' trip.pk %}" class="btn btn-primary w-100">Zobacz szczegóły</a>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12 d-flex justify-content-center align-items-center" style="min-height: 300px; width: 100%;">
            <p class="text-center fs-4">Nie znaleziono wycieczek spełniających kryteria.</p>
        </div>
        {% endfor %}
    </div>

    <!-- Paginacja -->
    <div class="pagination justify-content-center mt-4">
        <span class="step-links">
            {% if trips.has_previous %}
                <a href="?{% if request.GET.destination %}destination={{ request.GET.destination }}&{% endif %}{% if request.GET.min_price %}min_price={{ request.GET.min_price }}&{% endif %}{% if request.GET.max_price %}max_price={{ request.GET.max_price }}&{% endif %}{% if request.GET.start_date %}start_date={{ request.GET.start_date }}&{% endif %}{% if request.GET.end_date %}end_date={{ request.GET.end_date }}&{% endif %}{% if request.GET.transport %}transport={{ request.GET.transport }}&{% endif %}{% if request.GET.trip_type %}trip_type={{ request.GET.trip_type }}&{% endif %}{% if request.GET.meal_plan %}meal_plan={{ request.GET.meal_plan }}&{% endif %}page=1" class="btn btn-outline-primary">« pierwsza</a>
                <a href="?{% if request.GET.destination %}destination={{ request.GET.destination }}&{% endif %}{% if request.GET.min_price %}min_price={{ request.GET.min_price }}&{% endif %}{% if request.GET.max_price %}max_price={{ request.GET.max_price }}&{% endif %}{% if request.GET.start_date %}start_date={{ request.GET.start_date }}&{% endif %}{% if request.GET.end_date %}end_date={{ request.GET.end_date }}&{% endif %}{% if request.GET.transport %}transport={{ request.GET.transport }}&{% endif %}{% if request.GET.trip_type %}trip_type={{ request.GET.trip_type }}&{% endif %}{% if request.GET.meal_plan %}meal_plan={{ request.GET.meal_plan }}&{% endif %}page={{ trips.previous_page_number }}" class="btn btn-outline-primary">poprzednia</a>
            {% endif %}

            <span class="current mx-2">
                Strona {{ trips.number }} z {{ trips.paginator.num_pages }}
            </span>

            {% if trips.has_next %}
                <a href="?{% if request.GET.destination %}destination={{ request.GET.destination }}&{% endif %}{% if request.GET.min_price %}min_price={{ request.GET.min_price }}&{% endif %}{% if request.GET.max_price %}max_price={{ request.GET.max_price }}&{% endif %}{% if request.GET.start_date %}start_date={{ request.GET.start_date }}&{% endif %}{% if request.GET.end_date %}end_date={{ request.GET.end_date }}&{% endif %}{% if request.GET.transport %}transport={{ request.GET.transport }}&{% endif %}{% if request.GET.trip_type %}trip_type={{ request.GET.trip_type }}&{% endif %}{% if request.GET.meal_plan %}meal_plan={{ request.GET.meal_plan }}&{% endif %}page={{ trips.next_page_number }}" class="btn btn-outline-primary">następna</a>
                <a href="?{% if request.GET.destination %}destination={{ request.GET.destination }}&{% endif %}{% if request.GET.min_price %}min_price={{ request.GET.min_price }}&{% endif %}{% if request.GET.max_price %}max_price={{ request.GET.max_price }}&{% endif %}{% if request.GET.start_date %}start_date={{ request.GET.start_date }}&{% endif %}{% if request.GET.end_date %}end_date={{ request.GET.end_date }}&{% endif %}{% if request.GET.transport %}transport={{ request.GET.transport }}&{% endif %}{% if request.GET.trip_type %}trip_type={{ request.GET.trip_type }}&{% endif %}{% if request.GET.meal_plan %}meal_plan={{ request.GET.meal_plan }}&{% endif %}page={{ trips.paginator.num_pages }}" class="btn btn-outline-primary">ostatnia »</a>
            {% endif %}
        </span>
    </div>
</section>
</div>
{% endblock %}