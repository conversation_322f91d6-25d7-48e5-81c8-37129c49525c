{% extends 'base.html' %}

{% block title %}Lista zgłoszeń{% endblock %}

{% block content %}
<h2 class="mb-4">Lista wszystkich zgłoszeń</h2>

<table class="table table-striped table-hover table-rounded">
    <thead class="table-custom bg-danger">
        <tr>
            <th>ID</th>
            <th>Użytkownik</th>
            <th>Email</th>
            <th>Temat</th>
            <th>Data utworzenia</th>
            <th>Ostatnia aktualizacja</th>
            <th>Status</th>
            <th>Akcje</th>
        </tr>
    </thead>
    <tbody>
        {% for ticket in tickets %}
        <tr>
            <td>{{ ticket.id }}</td>
            <td>{% if ticket.user %}{{ ticket.user.username }}{% else %}-{% endif %}</td>
            <td>{{ user.email }}</td>
            <td>{{ ticket.subject }}</td>
            <td>{{ ticket.created_at|date:"d.m.Y H:i" }}</td>
            <td>{{ ticket.updated_at|date:"d.m.Y H:i" }}</td>
            <td>
                <form method="POST">
                    {% csrf_token %}
                    <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
                    <select name="status" class="form-select" onchange="this.form.submit()">
                        {% for value, label in ticket.STATUS_CHOICES %}
                            <option value="{{ value }}" {% if ticket.status == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </form>
            </td>
            <td>
                <div class="btn-group" role="group">
                    <a href="{% url 'ticket_detail' ticket.id %}" class="btn btn-sm btn-info">
                        <i class="bi bi-eye"></i> Szczegóły
                    </a>
                    <a href="{% url 'delete_ticket' ticket.id %}" class="btn btn-sm btn-danger">
                        <i class="bi bi-trash"></i> Usuń
                    </a>
                </div>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8">Brak zgłoszeń</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endblock %}
