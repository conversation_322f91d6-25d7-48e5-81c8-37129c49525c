from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST, require_http_methods
from django.contrib.auth import login
from django.contrib import messages
from django.conf import settings
import logging
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Country, Destination, Trip, Reservation, ReservationPerson, Review, Ticket, Message, ChatMessage, UserPreference, UserBrowsingHistory
from .forms import UserRegistrationForm, ReservationForm, ReviewForm, TripSearchForm, CountrySearchForm, NumberOfPeopleForm, ReservationPersonForm, TicketForm, MessageForm, ChatForm, CaptchaForm, UserPreferenceForm
from django.http import HttpResponseForbidden
from django.forms import modelformset_factory
import json
import logging
import google.generativeai as genai
from django.core.signing import TimestampSigner, BadSignature, SignatureExpired
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from . import chatbot
from .recommendation import track_user_view, get_trip_recommendations, get_recommendation_explanation
from .business_analysis import get_business_analysis_data
import datetime
from django.http import FileResponse, HttpResponseForbidden
import os



logger = logging.getLogger(__name__)



def is_superuser(user):
    return user.is_superuser

def update_trip_status():

    today = datetime.datetime.now().date()
    expired_trips = Trip.objects.filter(is_active=True, start_date__lt=today)
    expired_trips.update(is_active=False)
    return expired_trips.count()

def home(request):

    updated_count = update_trip_status()
    if updated_count > 0:
        logger.info(f"Oznaczono {updated_count} wycieczek jako nieaktywne, ponieważ ich data rozpoczęcia już minęła.")

    if request.user.is_authenticated:

        try:
            # Używamy synchronicznej funkcji zamiast asynchronicznej
            recommendations = get_trip_recommendations(request.user, count=3)

            # Pobierz wyjaśnienia dla rekomendacji
            recommendations_with_reasons = []
            for trip in recommendations:
                explanation = get_recommendation_explanation(request.user, trip)
                recommendations_with_reasons.append({
                    'trip': trip,
                    'reason': explanation
                })

        except Exception as e:
            print(f"Error getting recommendations: {e}")
            recommendations_with_reasons = []

        # Pobierz historię przeglądania
        recent_history = UserBrowsingHistory.objects.filter(
            user=request.user
        ).order_by('-last_viewed')[:3]

        # Pobierz nadchodzące rezerwacje
        upcoming_reservations = Reservation.objects.filter(
            user=request.user,
            trip__start_date__gte=datetime.datetime.now().date(),
            status='confirmed'
        ).order_by('trip__start_date')[:3]

        # Featured trips nadal pokazujemy
        featured_trips = Trip.objects.filter(is_active=True, available_places__gt=0).order_by('start_date')[:3]

        return render(request, 'home.html', {
            'trips': featured_trips,
            'recommendations': recommendations_with_reasons,
            'browsing_history': recent_history,
            'upcoming_reservations': upcoming_reservations,
        })
    else:
        # Dla niezalogowanych użytkowników, pokaż tylko featured trips
        featured_trips = Trip.objects.filter(is_active=True, available_places__gt=0).order_by('start_date')[:3]
        return render(request, 'home.html', {'trips': featured_trips})

def country_list(request):
    form = CountrySearchForm(request.GET or None)
    countries = Country.objects.all().order_by('name')

    if form.is_valid():
        search_name = form.cleaned_data.get('name')
        continent = form.cleaned_data.get('continent')

        if search_name:
            countries = countries.filter(name__icontains=search_name)

        if continent:
            countries = countries.filter(continent=continent)

    paginator = Paginator(countries, 3)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'country_list.html', {
        'form': form,
        'countries': page_obj,
    })

def country_destinations(request, country_id):
    country = get_object_or_404(Country, pk=country_id)
    destinations = Destination.objects.filter(country=country)

    return render(request, 'country_destinations.html', {
        'country': country,
        'destinations': destinations
    })

def trip_list(request, destination_id=None):

    update_trip_status()

    form = TripSearchForm(request.GET)
    trips = Trip.objects.filter(is_active=True, available_places__gt=0)

    if destination_id:
        destination = get_object_or_404(Destination, pk=destination_id)
        trips = trips.filter(destination=destination)
    else:
        destination = None

    if form.is_valid():
        if form.cleaned_data['destination']:
            trips = trips.filter(
                Q(destination__name__icontains=form.cleaned_data['destination']) |
                Q(destination__country__name__icontains=form.cleaned_data['destination'])
            )
        if form.cleaned_data['min_price']:
            trips = trips.filter(price__gte=form.cleaned_data['min_price'])
        if form.cleaned_data['max_price']:
            trips = trips.filter(price__lte=form.cleaned_data['max_price'])
        if form.cleaned_data['start_date']:
            trips = trips.filter(start_date__gte=form.cleaned_data['start_date'])
        if form.cleaned_data['end_date']:
            trips = trips.filter(end_date__lte=form.cleaned_data['end_date'])
        if form.cleaned_data['transport']:
            trips = trips.filter(transport=form.cleaned_data['transport'])
        if form.cleaned_data['trip_type']:
            trips = trips.filter(trip_type=form.cleaned_data['trip_type'])
        if form.cleaned_data['meal_plan']:
            trips = trips.filter(meal_plan=form.cleaned_data['meal_plan'])

    paginator = Paginator(trips, 6)
    page = request.GET.get('page')
    trips = paginator.get_page(page)

    return render(request, 'trip_list.html', {
        'trips': trips,
        'form': form,
        'destination': destination
    })

def trip_detail(request, pk):
    # Aktualizuj status wycieczek
    update_trip_status()

    trip = get_object_or_404(Trip, pk=pk)
    reviews = trip.review_set.all()

    # Track this view for recommendations
    if request.user.is_authenticated:
        track_user_view(request.user, trip)

    if request.method == 'POST':
        form = NumberOfPeopleForm(request.POST)
        if form.is_valid():
            number_of_people = form.cleaned_data['number_of_people']
            request.session['reservation_data'] = {
                'trip_id': trip.id,
                'number_of_people': number_of_people
            }
            return redirect('making_reservation')
    else:
        form = NumberOfPeopleForm()

    review_form = ReviewForm()

    # If user is authenticated, get similar trip recommendations
    similar_trips = None
    if request.user.is_authenticated:
        try:
            # Use the synchronous recommendation function
            similar_trips = get_trip_recommendations(request.user, count=3)

            # Exclude the current trip from recommendations
            similar_trips = [t for t in similar_trips if t.id != trip.id][:3]
        except Exception as e:
            print(f"Error getting similar trips: {e}")

    if not similar_trips:
        # Fallback to random similar trips based on destination or country
        similar_trips = Trip.objects.filter(
            is_active=True,
            available_places__gt=0
        ).exclude(id=trip.id)

        # Try to find trips with the same destination
        destination_trips = similar_trips.filter(destination=trip.destination)
        if destination_trips.exists():
            similar_trips = destination_trips
        else:
            # Try to find trips in the same country
            country_trips = similar_trips.filter(destination__country=trip.destination.country)
            if country_trips.exists():
                similar_trips = country_trips

        similar_trips = similar_trips[:3]

    return render(request, 'trip_detail.html', {
        'trip': trip,
        'reviews': reviews,
        'number_form': form,
        'review_form': review_form,
        'similar_trips': similar_trips,
    })

@login_required
def making_reservation(request):
    reservation_data = request.session.get('reservation_data')
    if not reservation_data:
        return redirect('trip_list')

    trip_id = reservation_data['trip_id']
    number_of_people = reservation_data['number_of_people']

    trip = get_object_or_404(Trip, id=trip_id)

    MyReservationPersonFormSet = modelformset_factory(
        ReservationPerson,
        form=ReservationPersonForm,
        extra=number_of_people,
        can_delete=False,
    )

    if request.method == 'POST':
        formset = MyReservationPersonFormSet(request.POST, queryset=ReservationPerson.objects.none())
        captcha_form = CaptchaForm(request.POST)
        if formset.is_valid() and captcha_form.is_valid():
            reservation = Reservation.objects.create(
                user=request.user,
                trip=trip,
                number_of_people=number_of_people,
                total_price=trip.price * number_of_people,
                status='pending'
            )
            for form in formset:
                if form.cleaned_data:
                    person = form.save(commit=False)
                    person.reservation = reservation
                    person.save()

            trip.available_places -= number_of_people
            if trip.available_places <= 0:
                trip.available_places = 0
                trip.is_active = False
            trip.save()
            request.session.pop('reservation_data', None)
            messages.success(request, f'Rezerwacja została złożona pomyślnie. Twój kod rezerwacji: {reservation.reservation_code}')
            return redirect('reservation_detail', reservation_id=reservation.id)
        else:
            if not captcha_form.is_valid():
                messages.error(request, 'Nieprawidłowa CAPTCHA.')
            else:
                messages.error(request, 'Błąd w formularzu. Sprawdź poprawność danych.')
    else:
        formset = MyReservationPersonFormSet(queryset=ReservationPerson.objects.none())
        captcha_form = CaptchaForm()

    return render(request, 'making_reservation.html', {
        'trip': trip,
        'formset': formset,
        'number_of_people': number_of_people,
        'captcha_form': captcha_form,
    })

@login_required
def add_review(request, trip_id):
    trip = get_object_or_404(Trip, pk=trip_id)

    if Review.objects.filter(user=request.user, trip=trip).exists():
        messages.error(request, 'Możesz dodać tylko jedną opinię dla tego wyjazdu.')
        return redirect('trip_detail', pk=trip_id)

    if request.method == 'POST':
        form = ReviewForm(request.POST)
        if form.is_valid():
            review = form.save(commit=False)
            review.user = request.user
            review.trip = trip
            review.save()
            messages.success(request, 'Dziękujemy za dodanie opinii!')
            return redirect('trip_detail', pk=trip_id)
        else:
            messages.error(request, 'Formularz zawiera błędy, popraw je i spróbuj ponownie.')
            return render(request, 'trip_detail.html', {'trip': trip, 'form': form})
    else:
        form = ReviewForm()
        return render(request, 'trip_detail.html', {'trip': trip, 'form': form})

@login_required
def delete_review(request, trip_id, review_id):
    trip = get_object_or_404(Trip, id=trip_id)
    review = get_object_or_404(Review, id=review_id, trip=trip)

    if request.user == review.user or request.user.is_superuser:
        review.delete()
        messages.success(request, "Opinia została pomyślnie usunięta.")
        return redirect('trip_detail', pk=trip.id)
    else:
        return HttpResponseForbidden("Nie masz uprawnień do usunięcia tej opinii.")

@login_required
def user_reservations(request):
    reservations = Reservation.objects.filter(user=request.user).order_by('-created_at')
    return render(request, 'user_reservations.html', {'reservations': reservations})

@login_required
def reservation_detail(request, reservation_id):
    reservation = get_object_or_404(Reservation, pk=reservation_id)
    if reservation.user != request.user and not request.user.is_superuser:
        return HttpResponseForbidden("Nie masz uprawnień do przeglądania tej rezerwacji.")

    reservation_people = ReservationPerson.objects.filter(reservation=reservation)
    signer = TimestampSigner()
    captcha_valid = False

    if request.user.is_superuser:
        # Check for signed cookie
        try:
            if 'captcha' in request.COOKIES:
                value = signer.unsign(request.COOKIES['captcha'], max_age=300)
                if value == 'verified':
                    captcha_valid = True
        except (BadSignature, SignatureExpired):
            pass

        # Check session variable as well
        if request.session.get('captcha_verified', False) and captcha_valid:
            captcha_valid = True
        else:
            captcha_valid = False

    return render(request, 'reservation_detail.html', {
        'reservation': reservation,
        'reservation_people': reservation_people,
        'captcha_valid': captcha_valid,
    })

@login_required
def delete_reservation(request, reservation_id):
    reservation = get_object_or_404(Reservation, pk=reservation_id)

    if reservation.user != request.user and not request.user.is_superuser:
        return HttpResponseForbidden("Nie masz uprawnień do usunięcia tej rezerwacji.")

    if request.method == 'POST':
        trip = reservation.trip

        if reservation.status != 'cancelled':
            trip.available_places += reservation.number_of_people
            trip.is_active = True
            trip.save()

        reservation.delete()
        messages.success(request, "Rezerwacja została pomyślnie usunięta.")

        if request.user.is_superuser:
            return redirect('administrator_all_reservations')
        else:
            return redirect('user_reservations')

    return render(request, 'confirm_delete_reservation.html', {
        'reservation': reservation,
    })

def register(request):
    if request.method == 'POST':
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, 'Rejestracja zakończona pomyślnie!')
            return redirect('home')
    else:
        form = UserRegistrationForm()
    return render(request, 'registration/register.html', {'form': form})

def create_ticket(request):
    if request.method == 'POST':
        form = TicketForm(request.POST)

        if form.is_valid():
            form.save(user=request.user)
            messages.success(request, 'Twoje zgłoszenie zostało zarejestrowane. Dziękujemy!')
            return redirect('home')
    else:
        form = TicketForm()

    return render(request, 'create_ticket.html', {'form': form})

@login_required
def ticket_detail(request, ticket_id):
    ticket = get_object_or_404(Ticket, id=ticket_id)
    messages_list = ticket.messages.all().order_by('created_at')

    if request.method == 'POST':
        form = MessageForm(request.POST)
        if form.is_valid():
            msg = form.save(commit=False)
            msg.ticket = ticket
            msg.user = request.user
            msg.save()
            messages.success(request, 'Twoja wiadomość została wysłana.')
            return redirect('ticket_detail', ticket_id=ticket.id)
    else:
        form = MessageForm()

    return render(request, 'ticket_detail.html', {
        'ticket': ticket,
        'ticket_messages': messages_list,
        'form': form,
    })

@login_required
def delete_ticket(request, ticket_id):
    ticket = get_object_or_404(Ticket, id=ticket_id)
    ticket.delete()
    messages.success(request, "Zgłoszenie zostało usunięte.")
    return redirect('my_tickets')

@login_required
def my_tickets(request):
    tickets = Ticket.objects.filter(user=request.user).order_by('-created_at')
    return render(request, 'my_tickets.html', {'tickets': tickets})

def chatbot_view(request):
    """Widok strony czatu"""
    context = {}

    # Pobierz historię czatu jeśli użytkownik jest zalogowany
    if request.user.is_authenticated:
        chat_history = chatbot.get_chat_history(request.user)
        context['chat_history'] = chat_history

    return render(request, 'chat.html', context)

@require_POST
def ask_question(request):
    """Widok API do zadawania pytań chatbotowi"""
    question = request.POST.get('question', '')

    if not question:
        return JsonResponse({
            'status': 'error',
            'message': 'Pytanie jest wymagane'
        })

    # Pobierz odpowiedź od chatbota
    response = chatbot.get_chatbot_response(request.user, question)

    return JsonResponse({
        'status': 'success',
        'response': response
    })

@login_required
@require_POST
def clear_chat_history(request):
    """Widok do czyszczenia historii czatu"""
    success = chatbot.clear_chat_history(request.user)

    if success:
        return JsonResponse({
            'status': 'success',
            'message': 'Historia czatu została wyczyszczona'
        })
    else:
        return JsonResponse({
            'status': 'error',
            'message': 'Nie udało się wyczyścić historii czatu'
        })

@login_required
def user_preferences(request):
    """View for managing user preferences"""
    try:
        preference = UserPreference.objects.get(user=request.user)
    except UserPreference.DoesNotExist:
        preference = UserPreference(user=request.user)

    if request.method == 'POST':
        form = UserPreferenceForm(request.POST, instance=preference)
        if form.is_valid():
            form.save()
            messages.success(request, "Twoje preferencje zostały zaktualizowane.")
            return redirect('home')
    else:
        form = UserPreferenceForm(instance=preference)

    return render(request, 'user_preferences.html', {
        'form': form
    })

@login_required
def download_reservation_pdf(request, reservation_id):
    reservation = get_object_or_404(Reservation, pk=reservation_id)
    # tylko właściciel lub superuser
    if reservation.user != request.user and not request.user.is_superuser:
        return HttpResponseForbidden("Brak dostępu do tego pliku.")

    # czy dokument istnieje?
    if not reservation.reservation_document:
        messages.error(request, "Dokument potwierdzenia nie jest dostępny.")
        return redirect('user_reservations')

    # zwróć plik jako załącznik
    file_path = reservation.reservation_document.path
    filename = os.path.basename(file_path)
    response = FileResponse(open(file_path, 'rb'),
                            as_attachment=True,
                            filename=filename)
    return response

@user_passes_test(is_superuser)
def administrator_dashboard(request):
    # Aktualizuj status wycieczek
    updated_count = update_trip_status()
    if updated_count > 0:
        messages.info(request, f"Oznaczono {updated_count} wycieczek jako nieaktywne, ponieważ ich data rozpoczęcia już minęła.")

    pending_reservations_count = Reservation.objects.filter(status='pending').count()
    all_reservations_count = Reservation.objects.count()
    active_trips = Trip.objects.filter(is_active=True).count()
    tickets_count = Ticket.objects.count()

    return render(request, 'administrator/dashboard.html', {
        'pending_reservations': pending_reservations_count,
        'all_reservations': all_reservations_count,
        'active_trips': active_trips,
        'tickets_count': tickets_count,
    })

@user_passes_test(is_superuser)
def administrator_pending_reservations(request):
    reservations = Reservation.objects.filter(status='pending').order_by('-created_at')
    signer = TimestampSigner()
    captcha_valid = False

    if request.method == 'POST':
        captcha_form = CaptchaForm(request.POST)
        if captcha_form.is_valid():
            request.session['captcha_verified'] = True
            signed_value = signer.sign('verified')
            response = redirect('administrator_pending_reservations')  # Redirect to the same view
            response.set_cookie('captcha', signed_value, max_age=300)
            messages.success(request, "Weryfikacja CAPTCHA przebiegła pomyślnie.")
            return response
        else:
            messages.error(request, "Nieprawidłowa CAPTCHA.")
    else:
        captcha_form = CaptchaForm()

        try:
            if 'captcha' in request.COOKIES:
                value = signer.unsign(request.COOKIES['captcha'], max_age=300)
                if value == 'verified':
                    captcha_valid = True
        except (BadSignature, SignatureExpired):
            pass

        if request.session.get('captcha_verified', False) and captcha_valid:
            captcha_valid = True
        else:
            captcha_valid = False
            if 'captcha_verified' in request.session:
                del request.session['captcha_verified']

    return render(request, 'administrator/pending_reservations.html', {
        'reservations': reservations,
        'captcha_form': captcha_form,
        'captcha_valid': captcha_valid,
    })

@user_passes_test(is_superuser)
def administrator_all_reservations(request):
    reservations = Reservation.objects.all().order_by('-created_at')
    signer = TimestampSigner()
    captcha_valid = False

    if request.method == 'POST':
        captcha_form = CaptchaForm(request.POST)
        if captcha_form.is_valid():
            request.session['captcha_verified'] = True
            signed_value = signer.sign('verified')
            response = redirect('administrator_all_reservations')  # Redirect to the same view
            response.set_cookie('captcha', signed_value, max_age=300)
            messages.success(request, "Weryfikacja CAPTCHA przebiegła pomyślnie.")
            return response
        else:
            messages.error(request, "Nieprawidłowa CAPTCHA.")
    else:
        captcha_form = CaptchaForm()

        try:
            if 'captcha' in request.COOKIES:
                value = signer.unsign(request.COOKIES['captcha'], max_age=300)
                if value == 'verified':
                    captcha_valid = True
        except (BadSignature, SignatureExpired):
            pass

        if request.session.get('captcha_verified', False) and captcha_valid:
            captcha_valid = True
        else:
            captcha_valid = False
            if 'captcha_verified' in request.session:
                del request.session['captcha_verified']

    return render(request, 'administrator/all_reservations.html', {
        'reservations': reservations,
        'captcha_form': captcha_form,
        'captcha_valid': captcha_valid,
    })

@user_passes_test(is_superuser)
def administrator_reservation_status(request, reservation_id):
    reservation = get_object_or_404(Reservation, pk=reservation_id)

    if request.method == 'POST':
        old_status = reservation.status
        new_status = request.POST.get('status')

        if new_status in dict(Reservation.STATUS_CHOICES):
            reservation.status = new_status
            reservation.save()

            trip = reservation.trip

            # Aktualizacja dostępności miejsc
            if old_status == 'cancelled' and new_status != 'cancelled':
                if reservation.number_of_people <= trip.available_places:
                    trip.available_places -= reservation.number_of_people
                    if trip.available_places <= 0:
                        trip.available_places = 0
                        trip.is_active = False
                    trip.save()
                else:
                    messages.error(request, 'Nie można ustawić tego statusu - brak wystarczających wolnych miejsc.')
                    reservation.status = old_status
                    reservation.save()

            elif old_status != 'cancelled' and new_status == 'cancelled':
                trip.available_places += reservation.number_of_people
                trip.is_active = True
                trip.save()

            # Generowanie dokumentu potwierdzającego, jeśli status zmienił się na 'confirmed'
            if new_status == 'confirmed':
                from .gen_reservation_document import generate_reservation_document
                success = generate_reservation_document(reservation)
                if success:
                    messages.success(request, 'Status rezerwacji został zaktualizowany. Dokument potwierdzający został wygenerowany.')
                else:
                    messages.warning(request, 'Status rezerwacji został zaktualizowany, ale wystąpił problem z generowaniem dokumentu potwierdzającego.')

            # Usuwanie dokumentu PDF, jeśli status zmienił się na 'cancelled' lub 'pending'
            elif new_status in ['cancelled', 'pending'] and old_status == 'confirmed':
                if reservation.reservation_document:
                    reservation.reservation_document.delete(save=True)
                messages.success(request, 'Status rezerwacji został zaktualizowany. Dokument został usunięty.')

            else:
                messages.success(request, 'Status rezerwacji został zaktualizowany.')

    return redirect('administrator_all_reservations')


@user_passes_test(is_superuser)
def list_tickets_admin(request):
    if request.method == "POST":
        ticket_id = request.POST.get("ticket_id")
        new_status = request.POST.get("status")

        ticket = get_object_or_404(Ticket, id=ticket_id)

        if new_status in dict(Ticket.STATUS_CHOICES):
            ticket.status = new_status
            ticket.save()
            messages.success(request, f"Status zgłoszenia #{ticket.id} został zmieniony.")
        else:
            messages.error(request, "Niepoprawny status.")

        return redirect("list_tickets_admin")

    tickets = Ticket.objects.all().order_by('-created_at')
    return render(request, 'administrator/admin_tickets_list.html', {'tickets': tickets})

@user_passes_test(is_superuser)
def business_analysis(request):
    """
    Widok analizy biznesowej dla administratorów
    """
    # Pobierz dane do analizy
    analysis_data = get_business_analysis_data()

    return render(request, 'administrator/business_analysis.html', {
        'analysis_data': analysis_data,
    })
