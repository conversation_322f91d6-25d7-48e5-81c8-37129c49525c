# Generated by Django 5.1.4 on 2025-03-02 17:14

import django.core.validators
import encrypted_model_fields.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('omasta_travel', '0022_alter_ticket_subject'),
    ]

    operations = [
        migrations.AlterField(
            model_name='reservationperson',
            name='pesel',
            field=encrypted_model_fields.fields.EncryptedCharField(help_text='PESEL musi mieć 11 cyfr.', validators=[django.core.validators.MinLengthValidator(11, message='PESEL musi mieć 11 cyfr.'), django.core.validators.MaxLengthValidator(11, message='PESEL musi mieć 11 cyfr.'), django.core.validators.RegexValidator('^\\d+$', message='PESEL może zawierać tylko cyfry.')]),
        ),
    ]
