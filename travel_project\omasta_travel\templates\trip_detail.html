{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{{ trip.title }} - Omasta Travel{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Header Section -->
    <div class="row align-items-center mb-5">
        <div class="col-lg-6">
            <h1 class="display-4 mb-3">{{ trip.title }}</h1>
            <p class="lead mb-4">{{ trip.description }}</p>
        </div>

        <div class="col-lg-6">
            {% if trip.destination.image %}
                <div class="trip-image-container">
                    <img src="{{ trip.destination.image.url }}" 
                         alt="{{ trip.title }}" 
                         class="trip-main-image">
                </div>
            {% endif %}
        </div>
    </div>
</div>


    <!-- Trip Details & Booking -->
    <div class="row g-4">
        <!-- Trip Details -->
        <div class="col-lg-8">
            <div class="card border-0 h-100">
                <div class="card-body">
                    <h3 class="card-title h4 mb-4">Szczegóły wycieczki</h3>
                    
                    <div class="details-grid">
                        <div class="detail-item">
                            <i class="bi bi-geo-alt text-primary"></i>
                            <strong>Destynacja</strong>
                            <span>{{ trip.destination.name }}, {{ trip.destination.country }}</span>
                        </div>
                        
                        <div class="detail-item">
                            <i class="bi bi-calendar text-primary"></i>
                            <strong>Data</strong>
                            <span>{{ trip.start_date }} - {{ trip.end_date }}</span>
                        </div>
                        
                        <div class="detail-item">
                            <i class="bi bi-tag text-primary"></i>
                            <strong>Typ wycieczki</strong>
                            <span>{{ trip.get_trip_type_display }}</span>
                        </div>
                        
                        <div class="detail-item">
                            <i class="bi bi-cup-hot text-primary"></i>
                            <strong>Wyżywienie</strong>
                            <span>{{ trip.get_meal_plan_display }}</span>
                        </div>
                        
                        <div class="detail-item">
                            <i class="bi bi-airplane text-primary"></i>
                            <strong>Transport</strong>
                            <span>{{ trip.get_transport_display }}</span>
                        </div>
                        
                        <div class="detail-item">
                            <i class="bi bi-people text-primary"></i>
                            <strong>Dostępne miejsca</strong>
                            <span>{{ trip.available_places }}</span>
                        </div>
                    </div>

                    <div class="price-badge mt-4">
                        <span class="h2">{{ trip.price }} PLN</span>
                        <span class="text-muted">/osoba</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking Form -->
        <div class="col-lg-4">
            <div class="card border-0">
                <div class="card-body">
                    {% if user.is_authenticated %}
                        <h3 class="h5 mb-4">Zarezerwuj wycieczkę</h3>
                        <form method="post">
                            {% csrf_token %}
                            <div class="mb-3">
                                {{ number_form.as_p}}
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                Rezerwuj teraz
                            </button>
                        </form>
                    {% else %}
                        <div class="text-center">
                            <p class="mb-3">Aby zarezerwować, musisz się zalogować</p>
                            <a href="{% url 'login' %}" class="btn btn-outline-primary">
                                Zaloguj się
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Reviews Section -->
<!-- Reviews Section -->
<div class="recommended-trips mt-5">
    <div class="container">
        <h2 class="mb-4 text-center">Opinie ({{ reviews.count }})</h2>
        
        {% if user.is_authenticated %}
            <div class="card border-0 mb-4 shadow-sm rounded-lg">
                <div class="card-body">
                    <h5 class="card-title text-primary">Dodaj opinię</h5>
                    <form method="post" action="{% url 'add_review' trip.id %}">
                        {% csrf_token %}
                        
                        <div class="form-group mb-3">
                            <label for="id_rating" class="form-label">Ocena</label>
                            {{ review_form.rating|add_class:"form-control" }}
                        </div>

                        <div class="form-group mb-3">
                            <label for="id_comment" class="form-label">Komentarz</label>
                            {{ review_form.comment|add_class:"form-control" }}
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg mt-3">
                            Opublikuj opinię
                        </button>
                    </form>
                </div>
            </div>
        {% endif %}
    </div>
</div>
<div class="card mb-3">
    <div class="card-body">
        <h6 class="card-subtitle mb-2 text-muted">{{ review.user.username }}</h6>
        <div class="mb-2">
            {% for i in "12345" %}
                {% if forloop.counter <= review.rating %}
                    ⭐
                {% endif %}
            {% endfor %}
        </div>
        <p class="card-text">{{ review.comment }}</p>
        <small class="text-muted">{{ review.created_at|date:"d.m.Y" }}</small>

            <div class="reviews-container">
                {% for review in reviews %}
                    <div class="card border-0 mb-3">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div class="user-info">
                                    <div class="user-circle">
                                        {{ review.user.username|first|upper }}
                                    </div>
                                    <div class="ms-3">
                                        <h6 class="mb-1">{{ review.user.username }}</h6>
                                        <div class="rating">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= review.rating %}
                                                    ⭐
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                <small class="text-muted">{{ review.created_at|date:"d.m.Y" }}</small>
                            </div>
                            
                            <p class="card-text mt-3">{{ review.comment }}</p>

                            {% if user.is_authenticated and review.user == user or user.is_superuser %}
                                <div class="mt-3 text-end">
                                    <form method="post" 
                                          action="{% url 'delete_review' trip.id review.id %}" 
                                          class="d-inline">
                                        {% csrf_token %}
                                        <button type="submit" 
                                                class="btn btn-outline-danger btn-sm"
                                                onclick="return confirm('Czy na pewno chcesz usunąć tę opinię?')">
                                            <i class="bi bi-trash"></i> Usuń
                                        </button>
                                    </form>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% empty %}
                    <div class="text-center py-5">
                        <i class="bi bi-chat-square-text h2 text-muted"></i>
                        <p class="mt-3 text-muted">Brak opinii dla tej wycieczki.</p>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}