{% load static %}
{% load widget_tweaks %}
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Omasta Travel{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar sticky-top navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">Omasta Travel</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="tripsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Oferta
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'trip_list' %}">Wycieczki</a></li>
                            <li><a class="dropdown-item" href="{% url 'country_list' %}">Państwa</a></li>
                            <li><a class="dropdown-item" href="{% url 'user_preferences' %}">Preferencje użytkownika</a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="contactDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Kontakt
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'create_ticket' %}">Formularz Kontaktowy</a></li>
                            <li><a class="dropdown-item" href="{% url 'chat' %}">ChatBot</a></li>
                        </ul>
                    </li>
                </ul>

                <!-- Sekcja użytkownika -->
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        {% if user.is_superuser %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Panel admina
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'administrator_dashboard' %}">Dashboard</a></li>
                                    <li><a class="dropdown-item" href="{% url 'business_analysis' %}">Analiza Biznesowa</a></li>
                                    <li><a class="dropdown-item" href="{% url 'administrator_all_reservations' %}">Rezerwacje</a></li>
                                    <li><a class="dropdown-item" href="{% url 'list_tickets_admin' %}">Zgłoszenia</a></li>
                                </ul>
                            </li>
                        {% endif %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="profileDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Mój profil
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'user_reservations' %}">Moje Rezerwacje</a></li>
                                <li><a class="dropdown-item" href="{% url 'my_tickets' %}">Moje Wiadomości</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <form method="post" action="{% url 'logout' %}" style="display: inline;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-link nav-link" style="padding: 0; border: none; background: none; color: inherit; text-decoration: none;">
                                    Wyloguj się
                                </button>
                            </form>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'login' %}">Zaloguj się</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'register' %}">Zarejestruj się</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>


    {% if messages %}
    <div class="container mt-3">
        {% for message in messages %}
            <div class="alert {% if message.tags == 'success' %}alert-success{% elif message.tags == 'error' %}alert-danger{% endif %}">
                {{ message }}
            </div>
        {% endfor %}
    </div>
    {% endif %}

    <main class="container my-4">
        {% block content %}
        
        {% endblock %}
    </main>

    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p>&copy; 2025 Omasta Travel. Wszelkie prawa zastrzeżone.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
