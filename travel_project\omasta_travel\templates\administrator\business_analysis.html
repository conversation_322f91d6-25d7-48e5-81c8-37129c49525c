{% extends 'base.html' %}
{% load static %}

{% block title %}Analiza Biznesowa - Panel Administratora{% endblock %}

{% block styles %}
<style>
    .chart-container {
        margin-bottom: 30px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 20px;
    }
    
    .stats-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
    }
    
    .stats-card h3 {
        color: #333;
        font-size: 18px;
        margin-bottom: 15px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    
    .stats-value {
        font-size: 24px;
        font-weight: bold;
        color: #007bff;
    }
    
    .stats-label {
        font-size: 14px;
        color: #666;
    }
    
    .report-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .report-container h2 {
        color: #333;
        font-size: 24px;
        margin-bottom: 20px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    
    .report-content {
        font-size: 16px;
        line-height: 1.6;
        color: #333;
    }
    
    .report-content h3 {
        font-size: 20px;
        margin-top: 20px;
        margin-bottom: 10px;
        color: #007bff;
    }
    
    .report-content ul {
        padding-left: 20px;
    }
    
    .report-content li {
        margin-bottom: 10px;
    }
    
    .destination-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }
    
    .destination-name {
        font-weight: bold;
    }
    
    .destination-count {
        color: #007bff;
    }
    
    .destination-revenue {
        color: #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="mb-4">Analiza Biznesowa Biura Podróży</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'administrator_dashboard' %}">Panel Administratora</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Analiza Biznesowa</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <!-- Karty ze statystykami -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>Łączna liczba rezerwacji</h3>
                <div class="stats-value">{{ analysis_data.reservation_stats.total_reservations }}</div>
                <div class="stats-label">wszystkie rezerwacje</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>Całkowity przychód</h3>
                <div class="stats-value">{{ analysis_data.reservation_stats.total_revenue|floatformat:2 }} PLN</div>
                <div class="stats-label">z potwierdzonych rezerwacji</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>Średnia wartość rezerwacji</h3>
                <div class="stats-value">{{ analysis_data.reservation_stats.avg_reservation_value|floatformat:2 }} PLN</div>
                <div class="stats-label">wszystkie rezerwacje</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>Liczba zgłoszeń</h3>
                <div class="stats-value">{{ analysis_data.customer_service.total_tickets }}</div>
                <div class="stats-label">wszystkie zgłoszenia</div>
            </div>
        </div>
    </div>
    
    <!-- Wykresy -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="chart-container">
                <h2>Sprzedaż miesięczna</h2>
                {% if analysis_data.monthly_sales_chart %}
                    <img src="data:image/png;base64,{{ analysis_data.monthly_sales_chart }}" class="img-fluid" alt="Wykres sprzedaży miesięcznej">
                {% else %}
                    <p class="text-muted">Brak danych do wyświetlenia wykresu.</p>
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-container">
                <h2>Najpopularniejsze kierunki</h2>
                {% if analysis_data.top_destinations_chart %}
                    <img src="data:image/png;base64,{{ analysis_data.top_destinations_chart }}" class="img-fluid" alt="Wykres najpopularniejszych kierunków">
                {% else %}
                    <p class="text-muted">Brak danych do wyświetlenia wykresu.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="chart-container">
                <h2>Obsługa klienta</h2>
                {% if analysis_data.customer_service_chart %}
                    <img src="data:image/png;base64,{{ analysis_data.customer_service_chart }}" class="img-fluid" alt="Wykres obsługi klienta">
                {% else %}
                    <p class="text-muted">Brak danych do wyświetlenia wykresu.</p>
                {% endif %}
                
                {% if analysis_data.customer_service.avg_resolution_time %}
                    <div class="mt-3">
                        <p><strong>Średni czas rozwiązania zgłoszenia:</strong> {{ analysis_data.customer_service.avg_resolution_time|floatformat:1 }} dni</p>
                    </div>
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-container">
                <h2>Top 5 kierunków podróży</h2>
                {% if analysis_data.top_destinations %}
                    <div class="list-group">
                        {% for destination in analysis_data.top_destinations|slice:":5" %}
                            <div class="destination-item">
                                <span class="destination-name">{{ destination.trip__destination__name }} ({{ destination.trip__destination__country__name }})</span>
                                <span class="destination-count">{{ destination.count }} rezerwacji</span>
                                <span class="destination-revenue">{{ destination.revenue|floatformat:2 }} PLN</span>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">Brak danych o najpopularniejszych kierunkach.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Raport AI -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="report-container">
                <h2>Raport i rekomendacje AI</h2>
                <div class="report-content">
                    {% if analysis_data.ai_insights %}
                        {{ analysis_data.ai_insights|linebreaks }}
                    {% else %}
                        <p class="text-muted">Nie udało się wygenerować raportu AI. Spróbuj ponownie później.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
