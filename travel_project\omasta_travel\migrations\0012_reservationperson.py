# Generated by Django 5.1.4 on 2025-01-23 22:21

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('omasta_travel', '0011_remove_trip_max_people_per_room_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReservationPerson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=50)),
                ('last_name', models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ('pesel', models.Char<PERSON>ield(help_text='PESEL musi mieć 11 cyfr.', max_length=11, validators=[django.core.validators.MinLengthValidator(11), django.core.validators.MaxLengthValidator(11)])),
                ('is_adult', models.Bo<PERSON>anField(default=False, help_text='<PERSON><PERSON><PERSON><PERSON>, jeśli ta osoba jest dorosła.')),
                ('reservation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='people', to='omasta_travel.reservation')),
            ],
        ),
    ]
