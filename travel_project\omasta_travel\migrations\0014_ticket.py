# Generated by Django 5.1.4 on 2025-02-11 18:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('omasta_travel', '0013_remove_reservation_number_of_children'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Ticket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(help_text='Adres e-mail klienta, jeśli nie jest zalogowany lub chce podać inny adres.', max_length=254)),
                ('subject', models.CharField(help_text='Temat zgłoszenia.', max_length=200)),
                ('message', models.TextField(help_text='Treść pytania lub prośby.')),
                ('status', models.CharField(choices=[('open', 'Otwarte'), ('in_progress', 'W realizacji'), ('resolved', 'Rozwiązane'), ('closed', 'Zamknięte')], default='open', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(blank=True, help_text='Użytkownik, który zgłosił zapytanie (jeśli jest zalogowany).', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tickets', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
