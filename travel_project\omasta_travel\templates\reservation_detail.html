{% extends "base.html" %}

{% block content %}
<div class="reservation-detail">
<h2 class="mb-4">Szczegóły rezerwacji</h2>

<!-- Sekcja 1: Podstawowe informacje o rezerwacji -->
<div class="card mb-4" style="border-radius: 15px; overflow: hidden; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);">
    <div class="card-header" style="background-color: #1a3c5e; color: #ffffff; border-bottom: none; padding: 12px 20px;">
        <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informacje o rezerwacji</h5>
    </div>
    <div class="card-body" style="padding: 20px;">
        <div class="row">
            <div class="col-md-4">
                <div class="detail-item">
                    <i class="bi bi-123 text-primary"></i>
                    <strong>Numer rezerwacji:</strong> {{ reservation.reservation_code }}
                </div>
            </div>
            <div class="col-md-4">
                <div class="detail-item">
                    <i class="bi bi-calendar-event text-primary"></i>
                    <strong>Data rezerwacji:</strong> {{ reservation.created_at }}
                </div>
            </div>
            <div class="col-md-4">
                <div class="detail-item">
                    <i class="bi bi-info-circle text-primary"></i>
                    <strong>Status:</strong> {{ reservation.get_status_display }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sekcja 2: Informacje o wycieczce i destynacji -->
<div class="card mb-4" style="border-radius: 15px; overflow: hidden; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);">
    <div class="card-header" style="background-color: #1a3c5e; color: #ffffff; border-bottom: none; padding: 12px 20px;">
        <h5 class="mb-0"><i class="bi bi-geo-alt"></i> Informacje o wycieczce</h5>
    </div>
    <div class="card-body" style="padding: 20px;">
        <div class="row">
            <div class="col-md-4">
                <div class="detail-item">
                    <i class="bi bi-briefcase text-primary"></i>
                    <strong>Wycieczka:</strong> {{ reservation.trip.title }}
                </div>
            </div>
            <div class="col-md-4">
                <div class="detail-item">
                    <i class="bi bi-map text-primary"></i>
                    <strong>Destynacja:</strong> {{ reservation.trip.destination.name }}
                </div>
            </div>
            <div class="col-md-4">
                <div class="detail-item">
                    <i class="bi bi-geo-alt text-primary"></i>
                    <strong>Kraj:</strong> {{ reservation.trip.destination.country.name }} ({{ reservation.trip.destination.country.get_continent_display }})
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sekcja 3: Informacje o terminach -->
<div class="card mb-4" style="border-radius: 15px; overflow: hidden; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);">
    <div class="card-header" style="background-color: #1a3c5e; color: #ffffff; border-bottom: none; padding: 12px 20px;">
        <h5 class="mb-0"><i class="bi bi-calendar"></i> Terminy</h5>
    </div>
    <div class="card-body" style="padding: 20px;">
        <div class="row">
            <div class="col-md-6">
                <div class="detail-item">
                    <i class="bi bi-calendar text-primary"></i>
                    <strong>Data rozpoczęcia:</strong> {{ reservation.trip.start_date }}
                </div>
            </div>
            <div class="col-md-6">
                <div class="detail-item">
                    <i class="bi bi-calendar-check text-primary"></i>
                    <strong>Data zakończenia:</strong> {{ reservation.trip.end_date }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sekcja 4: Informacje o świadczeniach -->
<div class="card mb-4" style="border-radius: 15px; overflow: hidden; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);">
    <div class="card-header" style="background-color: #1a3c5e; color: #ffffff; border-bottom: none; padding: 12px 20px;">
        <h5 class="mb-0"><i class="bi bi-stars"></i> Świadczenia</h5>
    </div>
    <div class="card-body" style="padding: 20px;">
        <div class="row">
            <div class="col-md-6">
                <div class="detail-item">
                    <i class="bi bi-airplane text-primary"></i>
                    <strong>Transport:</strong> {{ reservation.trip.get_transport_display }}
                </div>
            </div>
            <div class="col-md-6">
                <div class="detail-item">
                    <i class="bi bi-cup-hot text-primary"></i>
                    <strong>Wyżywienie:</strong> {{ reservation.trip.get_meal_plan_display }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sekcja 5: Informacje o kosztach i uczestnikach -->
<div class="card mb-4" style="border-radius: 15px; overflow: hidden; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);">
    <div class="card-header" style="background-color: #1a3c5e; color: #ffffff; border-bottom: none; padding: 12px 20px;">
        <h5 class="mb-0"><i class="bi bi-people"></i> Uczestnicy i koszty</h5>
    </div>
    <div class="card-body" style="padding: 20px;">
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="detail-item">
                    <i class="bi bi-people text-primary"></i>
                    <strong>Liczba osób:</strong> {{ reservation.number_of_people }}
                </div>
            </div>
            <div class="col-md-6">
                <div class="detail-item">
                    <i class="bi bi-cash-stack text-primary"></i>
                    <strong>Cena całkowita:</strong> {{ reservation.total_price }} PLN
                </div>
            </div>
        </div>

        <h5 class="mt-4 mb-3"><i class="bi bi-person-badge"></i> Lista uczestników:</h5>
        <div class="table-responsive">
            <table class="table table-rounded">
                <thead class="table-custom">
                    <tr>
                        <th>Lp.</th>
                        <th>Imię i nazwisko</th>
                        <th>PESEL</th>
                    </tr>
                </thead>
                <tbody>
                    {% for person in reservation_people %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ person }}</td>
                        <td>
                            {% if request.user.is_superuser and captcha_valid %}
                                {{ person.pesel }}
                            {% else %}
                                {% if person.pesel %}
                                    {{ person.pesel|slice:":-3" }}###
                                {% else %}
                                    Brak danych
                                {% endif %}
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="mt-4">
    <a href="{% url 'user_reservations' %}" class="btn btn-outline-primary" style="border-radius: 30px; padding: 10px 25px;">
        <i class="bi bi-arrow-left"></i> Powrót do listy rezerwacji
    </a>
</div>
</div>
{% endblock %}
